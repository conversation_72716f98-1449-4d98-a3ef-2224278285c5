import { ApolloArmor } from '@escape.tech/graphql-armor'
import { ApolloServerPlugin } from '@apollo/server'

const armor = new ApolloArmor({
    maxDepth: {
        enabled: true,
        n: 7,
        flattenFragments: true,
    },
    costLimit: {
        enabled: false,
    },
})

const protect = armor.protect()

export const securityPlugins: ApolloServerPlugin[] = protect.plugins
export const validationRules = protect.validationRules
