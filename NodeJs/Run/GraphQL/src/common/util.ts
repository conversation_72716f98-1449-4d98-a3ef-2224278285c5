import TurndownService from 'turndown'
import LinkifyIt from 'linkify-it'

export function notEmpty<TValue>(value: TValue | null | undefined): value is TValue {
    return value !== null && value !== undefined
}

export const turndownService = new TurndownService()
    .addRule('paragraph-new-line', {
        filter: 'p',
        replacement: function (content) {
            return '\n' + content + '\n'
        },
    })
    // https://stackoverflow.com/questions/77555009/bold-does-not-display-correctly
    .addRule('strong-non-blocking-width', {
        filter: 'strong',
        replacement: function (content) {
            return '&#x200B;**' + content + '**&#x200B;'
        },
    })

/**
 * Converts urls links to markdown links
 * @param plainText
 */
export function plainTextToMarkdown(plainText: string) {
    let markdown = plainText

    const linkify = new LinkifyIt()
    const matches = linkify.match(markdown)
    if (!matches) {
        return markdown
    }

    for (const match of matches) {
        const simplifiedUrl = simplifyUrl(match.url)
        const markdownLink = `[${simplifiedUrl}](${match.url})`
        markdown = markdown.replace(match.text, markdownLink)
    }

    return markdown
}

function simplifyUrl(url: string) {
    let simpleUrl = cropUrl(url, 30)
    // remove common protocols from the beginning
    const removePrefix = (text: string, val: string) => (text.startsWith(val) ? text.replace(val, '') : text)
    simpleUrl = removePrefix(simpleUrl, 'http://')
    simpleUrl = removePrefix(simpleUrl, 'https://')
    simpleUrl = removePrefix(simpleUrl, 'www.')
    simpleUrl = removePrefix(simpleUrl, 'mailto:')
    // remove trailing slash
    if (simpleUrl.endsWith('/')) {
        simpleUrl = simpleUrl.slice(0, -1)
    }

    return simpleUrl
}

function cropUrl(url: string, length: number): string {
    if (url.length <= length) {
        return url
    }

    // Remove http:// or https://
    url = url.replace(/^https?:\/\//, '')

    // Remove www.
    url = url.replace(/^www\./, '')

    // Replace /foo/bar/foo/ with /…/…/…/
    let urlLength = url.length
    while (urlLength > length) {
        url = url.replace(/(.*[^/])\/[^/…]+\/([^/])/, '$1/…/$2')
        if (url.length === urlLength) {
            break
        } else {
            urlLength = url.length
        }
    }

    // Replace /…/…/…/ with /…/
    url = url.replace(/\/…\/(?:…\/)+/, '/…/')

    // Replace all params except the first
    let idx: number
    while (url.length > length) {
        idx = url.lastIndexOf('&')
        if (idx === -1) {
            break
        }
        url = url.substring(0, idx) + '…'
    }

    // Replace the first param
    if (url.length > length) {
        idx = url.lastIndexOf('?')
        if (idx !== -1) {
            url = url.substring(0, idx) + '?…'
        }
    }

    // Replace endless hyphens
    while (url.length > length) {
        idx = url.lastIndexOf('-')
        if (idx === -1) {
            break
        }
        url = url.substring(0, idx) + '…'
    }

    return url
}
