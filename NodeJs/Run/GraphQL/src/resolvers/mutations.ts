import { MutationResolvers, PostAssetInput, SubscriptionStatus } from '../generated/resolvers-types'
import { Subscriber, SubscriberStatus, UpdateSubscribeRequestType } from '../generated/api'
import { FullSubscriptionModel } from '../models/subscription'
import { unauthorizedError } from './utils'
import { z } from 'zod'
import { PostAssetModel } from '../models/post'
import { GraphQLError } from 'graphql/error'

const viewerUpdate: MutationResolvers['viewerUpdate'] = async (_, { userDetails }, { dataSources, user }) => {
    if (!user?.id) {
        throw unauthorizedError()
    }

    const currentUserDetails = await dataSources.userAPI.getUserDetails(user.id)

    let profileImage
    if (userDetails.profileImage === null) {
        profileImage = undefined
    } else {
        profileImage =
            userDetails.profileImage ??
            (currentUserDetails.image
                ? {
                      url: currentUserDetails.image.url,
                      width: currentUserDetails.image.width,
                      height: currentUserDetails.image.height,
                  }
                : undefined)
    }

    const emailPublic =
        userDetails.emailPublic === null ? undefined : (userDetails.emailPublic ?? currentUserDetails.emailPublic)

    const emailInvoice =
        userDetails.emailInvoice === null ? undefined : (userDetails.emailInvoice ?? currentUserDetails.emailInvoice)

    const request = {
        bio: userDetails.bio ?? currentUserDetails.bio,
        path: userDetails.path ?? currentUserDetails.path,
        name: userDetails.name ?? currentUserDetails.name,
        isOfAge: userDetails.isOfAge ?? currentUserDetails.isOfAge,
        profileImage,
        emailPublic,
        emailInvoice,
    }

    return await dataSources.userAPI.updateUsersDetails(user.id, request)
}

export const mutations: MutationResolvers = {
    assetTimestampUpdate: async (_, { assetId, timestamp, postId }, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        await dataSources.userMediaStoreAPI.updateAssetTimestamp(assetId, timestamp, postId)

        return null
    },

    categoryCreate: async (_, { input }, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        const categories = await dataSources.categoriesAPI.getCategories({ userId: user.id })
        const index = input.index ?? 0
        if (index > categories.length || index < 0) {
            throw Error('Invalid index')
        }

        const category = await dataSources.categoriesAPI.postCategory({ userId: user.id, categoryName: input.name })
        const categoryIds = categories.map((category) => category.id)
        categoryIds.splice(index, 0, category.id)

        await dataSources.categoriesAPI.postCategoriesOrder({ userId: user.id, categoryIds: categoryIds })
        return {
            category,
        }
    },

    categoryUpdate: async (_, { input, id }, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        const category = await dataSources.categoriesAPI.patchCategory({
            userId: user.id,
            categoryName: input.name,
            categoryId: id,
        })
        return {
            category,
        }
    },

    categoryDelete: async (_, { categoryId }, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        await dataSources.categoriesAPI.deleteCategory({ userId: user.id, categoryId })

        return {
            success: true,
        }
    },

    categoriesOrder: async (_, { input }, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        if (input.categoryIds.length == 0) {
            return {
                success: true,
            }
        }

        await dataSources.categoriesAPI.postCategoriesOrder({
            userId: user.id,
            categoryIds: input.categoryIds,
        })

        return {
            success: true,
        }
    },

    notificationUpdate: async (_, { id, input }, { dataSources }) => {
        await dataSources.notificationAPI.updateNotification(id, input.checkedAt, input.seenAt)

        return null
    },

    postAddToLibrary: async (_, { postId }, { dataSources }) => {
        const savedPost = await dataSources.libraryAPI.addPostToLibrary(postId)
        return {
            savedPost,
        }
    },

    postRemoveFromLibrary: async (_, { postId }, { dataSources }) => {
        await dataSources.libraryAPI.removePostFromLibrary(postId)

        return null
    },

    postDelete: async (_, { postId }, { dataSources }) => {
        await dataSources.postAPI.deletePost(postId)

        return null
    },

    postCreate: async (_, { attributes }, { dataSources }) => {
        const post = await dataSources.postAPI.createPost(attributes)

        return {
            post,
        }
    },

    postUpdate: async (_, { postId, attributes }, { dataSources }) => {
        // we just use this to validate that we actually got full details
        const postWithDetails = z.object({
            pinnedAt: z.string().optional().nullable(),
            publishedAt: z.string(),
            text: z.string(),
            textHtml: z.string(),
            isExcludedFromRss: z.boolean(),
        })

        const originalPost = await dataSources.postAPI.getPost(postId)
        const validatedPostResult = postWithDetails.safeParse(originalPost)
        if (!validatedPostResult.success) {
            throw unauthorizedError()
        }

        const validatedPost = validatedPostResult.data

        const pinnedAt =
            attributes.pinnedAt === undefined
                ? (validatedPost.pinnedAt ?? undefined)
                : (attributes.pinnedAt ?? undefined)
        const textDelta =
            attributes.textDelta === undefined
                ? (originalPost.textDelta ?? undefined)
                : (attributes.textDelta ?? undefined)

        const post = await dataSources.postAPI.updatePost(postId, {
            assets:
                attributes.assets ??
                originalPost.assets.map((asset) => {
                    return mapAssetToInput(asset)
                }),
            pinnedAt,
            textDelta,
            text: attributes.text ?? validatedPost.text,
            textHtml: attributes.textHtml ?? validatedPost.textHtml,
            categories: attributes.categories ?? originalPost.categories.map((category) => category.id),
            publishedAt: attributes.publishedAt ?? undefined,
            isSponsored: attributes.isSponsored ?? originalPost.isSponsored,
            isAgeRestricted: attributes.isAgeRestricted ?? originalPost.isAgeRestricted,
            isExcludedFromRss: attributes.isExcludedFromRss ?? validatedPost.isExcludedFromRss,
        })

        return {
            post,
        }
    },

    commentCreate: async (_, { parentId, siblingId, attributes }, { dataSources }) => {
        const comment = await dataSources.postAPI.createComment(parentId, attributes, siblingId ?? undefined)

        return {
            comment,
        }
    },

    commentUpdate: async (_, { commentId, attributes }, { dataSources }) => {
        const comment = dataSources.postAPI.updateComment(commentId, attributes)

        return {
            comment,
        }
    },

    commentDelete: async (_, { commentId }, { dataSources }) => {
        await dataSources.postAPI.deleteComment(commentId)

        return null
    },

    notificationMarkAllSeen: async (_, __, { dataSources, user }) => {
        if (!user?.id) {
            throw unauthorizedError()
        }
        await dataSources.notificationAPI.markAllSeen(user.id)

        return null
    },

    subscribeRequestCreate: async (_, { input }, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        await dataSources.subscribeRequestAPI.createSubscribeRequest(input.creatorId)

        return {
            success: true,
        }
    },

    subscribeRequestAccept: async (_, { id }, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        await dataSources.subscribeRequestAPI.updateSubscribeRequest(parseInt(id), UpdateSubscribeRequestType.ACCEPT)

        return {
            success: true,
        }
    },

    subscribeRequestAcceptAll: async (_, __, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        let after = null
        let hasNext = false
        do {
            const { subscribeRequests, pagination } = await dataSources.subscribeRequestAPI.getSubscribeRequests({
                first: 100,
                after,
            })

            const acceptSubscribeRequestPromises = subscribeRequests.map((subscribeRequest) =>
                dataSources.subscribeRequestAPI.updateSubscribeRequest(
                    subscribeRequest.id,
                    UpdateSubscribeRequestType.ACCEPT
                )
            )

            await Promise.all(acceptSubscribeRequestPromises)
            hasNext = pagination.hasNextPage
            after = pagination.endCursor
        } while (hasNext)

        return {
            success: true,
        }
    },

    subscribeRequestCancel: async (_, { creatorId }, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        const graphQLError = new GraphQLError(
            `User does not have a pending subscribe request to creator ${creatorId}`,
            {
                extensions: {
                    code: 'BAD_REQUEST',
                },
            }
        )
        let subscribeRequest = undefined
        try {
            subscribeRequest = await dataSources.subscribeRequestAPI.getSubscribeRequest(creatorId)
        } catch (_e) {
            throw graphQLError
        }
        if (!subscribeRequest) {
            throw graphQLError
        }

        if (subscribeRequest.userId !== user.id) {
            throw unauthorizedError()
        }

        if (subscribeRequest.acceptedAt || subscribeRequest.declinedAt || subscribeRequest.deletedAt) {
            throw graphQLError
        }

        await dataSources.subscribeRequestAPI.updateSubscribeRequest(
            subscribeRequest.id,
            UpdateSubscribeRequestType.DELETE
        )

        return {
            success: true,
        }
    },

    subscribeRequestDecline: async (_, { id }, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        await dataSources.subscribeRequestAPI.updateSubscribeRequest(parseInt(id), UpdateSubscribeRequestType.DECLINE)

        return {
            success: true,
        }
    },

    subscriptionCancel: async (_, { creatorId }, { dataSources, user }) => {
        if (!user?.id) {
            throw unauthorizedError()
        }

        const subscription = await dataSources.subscriptionAPI.getSubscription(user.id, creatorId)
        if (subscription.tier?.id == 'EUR00') {
            const response = await dataSources.subscriptionAPI.deleteSubscription(user.id, creatorId, user.role)
            return {
                subscription: mapToSubscription(subscription, response),
                success: true,
            }
        }

        const response =
            subscription.status == SubscriptionStatus.PAST_DUE
                ? await dataSources.subscriptionAPI.deleteSubscription(user.id, creatorId, user.role)
                : await dataSources.subscriptionAPI.patchSubscription(user.id, creatorId, { cancelAtPeriodEnd: true })

        return {
            subscription: mapToSubscription(subscription, response),
            success: true,
        }
    },

    subscriptionRenew: async (_, { creatorId }, { dataSources, user }) => {
        if (!user?.id) {
            throw unauthorizedError()
        }

        const subscription = await dataSources.subscriptionAPI.getSubscription(user.id, creatorId)
        if (!subscription.cancelAtPeriodEnd) {
            return {
                subscription,
                success: true,
            }
        }

        const response = await dataSources.subscriptionAPI.patchSubscription(user.id, creatorId, {
            cancelAtPeriodEnd: false,
        })
        return {
            subscription: mapToSubscription(subscription, response),
            success: true,
        }
    },

    subscriberDelete: async (_, { subscriberId }, { dataSources, user }) => {
        if (!user?.id) {
            throw unauthorizedError()
        }

        const subscriber = await dataSources.subscriptionAPI.getSubscriber(user.id, subscriberId)
        const response = await dataSources.subscriptionAPI.deleteSubscription(subscriberId, user.id, user.role)

        return {
            subscription: mapToSubscription(subscriber, response),
            success: true,
        }
    },

    sessionRevoke: async (_, __, { dataSources, user }) => {
        if (!user?.id) {
            throw unauthorizedError()
        }

        await dataSources.sessionAPI.deleteSessions()

        return {
            success: true,
        }
    },

    userDetailsUpdate: viewerUpdate,
    viewerUpdate: viewerUpdate,

    rssFeedUrlGenerate: async (_, { creatorId }, { dataSources }) => {
        const rssFeedUrl = await dataSources.userAPI.rssFeedUrl(creatorId)
        return {
            rssFeedUrl,
        }
    },

    notificationSettingsUpdate: async (_, { input }, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        const currentNotificationSettings = await dataSources.notificationSettingsAPI.getNotificationSettings()
        await dataSources.notificationSettingsAPI.updateNotificationSettings({
            emailNewDm: input.emailNewDm ?? currentNotificationSettings.emailNewDm,
            emailNewPost: input.emailNewPost ?? currentNotificationSettings.emailNewPost,
            newsletter: input.newsletter ?? currentNotificationSettings.newsletter,
            termsChanged: input.termsChanged ?? currentNotificationSettings.termsChanged,
            pushNewComment: input.pushNewComment ?? currentNotificationSettings.pushNewComment,
            pushNewPost: input.pushNewPost ?? currentNotificationSettings.pushNewPost,
        })

        return {
            success: true,
        }
    },
}

function mapStatus(restStatus: SubscriberStatus): SubscriptionStatus {
    switch (restStatus) {
        case SubscriberStatus.ACTIVE:
            return SubscriptionStatus.ACTIVE
        case SubscriberStatus.PAST_DUE:
            return SubscriptionStatus.PAST_DUE
        default:
            return SubscriptionStatus.INACTIVE
    }
}

function mapToSubscription(model: FullSubscriptionModel, response: Subscriber): FullSubscriptionModel {
    return {
        id: model.id,
        subscribedAt: model.subscribedAt,
        tier: model.tier,
        subscriber: model.subscriber,
        creator: model.creator,
        couponAppliedForMonths: model.couponAppliedForMonths,
        expires: response.expires,
        cancelAtPeriodEnd: response.cancelAtPeriodEnd,
        status: mapStatus(response.status),
        subscriptionModelType: 'full',
    }
}

function mapAssetToInput(asset: PostAssetModel): PostAssetInput {
    switch (asset.assetType) {
        case 'document':
            return {
                document: asset,
            }
        case 'gjirafa-livestream':
            return {
                gjirafaLivestream: asset,
            }

        case 'gjirafa':
            return {
                gjirafa: asset,
            }

        case 'image':
            return {
                image: asset,
            }
        case 'youtube':
            throw Error('Youtube posts cannot be updated anymore')
        case 'bunny':
            throw Error('Bunny posts cannot be updated anymore')
        case 'empty':
            throw Error('Got empty asset')
    }
}
