plugins {
    `kotlin-dsl`
}

repositories {
    // so that external plugins can be resolved in dependencies section
    gradlePluginPortal()
}

dependencies {
    implementation("com.gradleup.shadow:com.gradleup.shadow.gradle.plugin:_")
    implementation("org.jlleitschuh.gradle.ktlint:org.jlleitschuh.gradle.ktlint.gradle.plugin:_")
    implementation(Kotlin.gradlePlugin)
    implementation("org.jetbrains.kotlin.plugin.noarg:org.jetbrains.kotlin.plugin.noarg.gradle.plugin:_")
    implementation("io.gitlab.arturbosch.detekt:detekt-gradle-plugin:_")
    implementation("io.sentry.jvm.gradle:io.sentry.jvm.gradle.gradle.plugin:_")
}
