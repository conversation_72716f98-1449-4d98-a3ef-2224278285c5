package hero.stripe.payout.scripts

import com.stripe.param.ChargeUpdateParams
import com.stripe.param.InvoiceRetrieveParams
import com.stripe.param.SubscriptionSearchParams
import hero.baseutils.systemEnv
import hero.model.Currency
import hero.stripe.service.StripeClients
import java.time.Instant
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

fun main() {
    val stripeClients = StripeClients(
        keysEu = systemEnv("STRIPE_API_KEY_EU_PROD"),
        keysUs = systemEnv("STRIPE_API_KEY_EU_PROD"),
    )
    val executor = Executors.newFixedThreadPool(25)
    stripeClients[Currency.USD]
        .subscriptions()
        .search(
            SubscriptionSearchParams.builder()
                .setQuery("metadata['appFeeStripeFixed']:\"0.30\"")
                .build(),
        )
        .autoPagingIterable()
        .forEach {
            executor.submit {
                val invoice = stripeClients[Currency.USD].invoices().retrieve(
                    it.latestInvoice,
                    InvoiceRetrieveParams.builder().addExpand("charge").build(),
                )
                if (invoice.amountPaid == 0L) {
                    return@submit
                }
                val charge = invoice.chargeObject ?: error("Charge for ${invoice.id} is null.")
                val sourceMetadata = it.metadata!!
                charge.update(
                    ChargeUpdateParams.builder()
                        .setMetadata(
                            mapOf(
                                "appFeeHerohero" to sourceMetadata["appFeeHerohero"],
                                "appFeeStripeDynamic" to sourceMetadata["appFeeStripeDynamic"],
                                "appFeeStripeFixed" to sourceMetadata["appFeeStripeFixed"],
                            ),
                        )
                        .build(),
                )

                println(
                    "${it.id} ${charge.id} ${Instant.ofEpochSecond(it.created)} " +
                        "${it.currency} ${it.metadata["appFeeHerohero"]}",
                )
            }
        }
    executor.shutdown()
    executor.awaitTermination(Long.MAX_VALUE, TimeUnit.HOURS)
}
