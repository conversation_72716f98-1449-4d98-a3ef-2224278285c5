.Kotlin/Run/Connect/variables:
  variables:
    SERVICE_NAME: connect
    SENTRY_DSN: https://<EMAIL>/4509056059113472
    OTEL_SAMPLE_RATE: 0
    MEMORY: 1024Mi
    ENV_VARS: "CONNECT_SECRET_KEY=$CONNECT_SECRET_KEY"

Kotlin/Run/Connect/build:
  stage: build-services
  needs:
    - Kotlin/Modules/Http4k/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/Jwt/build
    - Kotlin/Modules/Exceptions/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/Run/Connect/variables
    - .Kotlin/job-build-gradle-service

Kotlin/Run/Connect/deploy-devel:
  stage: deploy-devel
  extends:
    - .Kotlin/Run/deploy-devel
    - .Kotlin/Run/Connect/variables
  needs:
    - Kotlin/Run/Connect/build

Kotlin/Run/Connect/deploy-staging:
  stage: deploy-staging
  extends:
    - .Kotlin/Run/deploy-staging
    - .Kotlin/Run/Connect/variables
  needs:
    - Kotlin/Run/Connect/build

Kotlin/Run/Connect/deploy-prod:
  stage: deploy-prod
  extends:
    - .Kotlin/Run/deploy-prod
    - .Kotlin/Run/Connect/variables
  needs:
    - Kotlin/Run/Connect/build
  variables:
    MIN_INSTANCES: 1
    MAX_INSTANCES: 1
