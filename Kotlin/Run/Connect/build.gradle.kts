plugins {
    id("hero.kotlin-run-service-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    implementation(Http4k.securityOauth)
    implementation(projectModule(":Modules:Model"))
    implementation(projectModule(":Modules:Exceptions"))
    implementation(projectModule(":Modules:Http4k"))
    implementation(projectModule(":Modules:SQL"))
    implementation(projectModule(":Modules:Repository"))
    implementation(projectModule(":Modules:BaseUtils"))
    implementation(projectModule(":Modules:Jwt"))

    testImplementation(projectModule(":Modules:IntegrationTesting"))
}
