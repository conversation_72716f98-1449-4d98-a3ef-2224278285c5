package hero.connect.oauth

import dev.forkhandles.result4k.Failure
import dev.forkhandles.result4k.Result
import dev.forkhandles.result4k.Success
import org.http4k.core.Request
import org.http4k.security.oauth.server.AccessTokenError
import org.http4k.security.oauth.server.ClientId
import org.http4k.security.oauth.server.InvalidClientAssertion
import org.http4k.security.oauth.server.InvalidClientCredentials
import org.http4k.security.oauth.server.TokenRequest
import org.http4k.security.oauth.server.UnsupportedGrantType
import org.http4k.security.oauth.server.accesstoken.AccessTokenRequestAuthentication
import org.http4k.security.oauth.server.accesstoken.GrantType
import org.jooq.DSLContext

class OAuthClientCredentialsValidator(
    lazyContext: Lazy<DSLContext>,
) : AccessTokenRequestAuthentication {
    private val context by lazyContext

    override fun validateCredentials(
        request: Request,
        tokenRequest: TokenRequest,
    ): Result<Triple<Request, ClientId, TokenRequest>, AccessTokenError> {
        val clientId = tokenRequest.clientId ?: return Failure(InvalidClientAssertion)
        val client = context.getClient(clientId.value) ?: return Failure(InvalidClientAssertion)

        if (client.secret != tokenRequest.clientSecret) {
            return Failure(InvalidClientCredentials)
        }

        if (tokenRequest.grantType != GrantType.AuthorizationCode) {
            return Failure(UnsupportedGrantType(tokenRequest.grantType.name))
        }

        return Success(Triple(request, clientId, tokenRequest))
    }
}
