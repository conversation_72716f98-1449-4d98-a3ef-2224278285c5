package hero.connect.oauth

import hero.baseutils.plusMinutes
import hero.jwt.parseJwt
import hero.jwt.toJwt
import io.jsonwebtoken.Claims
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Uri
import org.http4k.core.cookie.Cookie
import org.http4k.core.cookie.SameSite
import org.http4k.core.cookie.cookie
import org.http4k.lens.Cookies
import org.http4k.security.ResponseMode
import org.http4k.security.ResponseType
import org.http4k.security.State
import org.http4k.security.oauth.server.AuthRequest
import org.http4k.security.oauth.server.AuthRequestTracking
import org.http4k.security.oauth.server.ClientId
import java.time.Clock
import java.time.Instant
import java.util.Date
import javax.crypto.SecretKey

class OAuthRequestTracker(
    private val secretKey: SecretKey,
    private val clock: Clock = Clock.systemUTC(),
) : AuthRequestTracking {
    private val cookieName = "OauthFlowId"

    override fun trackAuthRequest(
        request: Request,
        authRequest: AuthRequest,
        response: Response,
    ): Response {
        val now = Instant.now(clock)
        val jwt = buildMap {
            put(CLIENT_ID, authRequest.client.value)
            put(REDIRECT_URI, authRequest.redirectUri?.toString())
            put(SCOPES, authRequest.scopes)
            put(STATE, authRequest.state?.value)
            put(RESPONSE_TYPE, authRequest.responseType.name)
            put(RESPONSE_MODE, authRequest.responseMode?.name)
            put(Claims.EXPIRATION, Date.from(now.plusSeconds(60 * 5)))
            put(Claims.ISSUED_AT, Date.from(now))
        }.toJwt(secretKey)

        return response.cookie(
            Cookie(cookieName, jwt)
                .secure()
                .httpOnly()
                .expires(now.plusMinutes(10))
                .path("/connect")
                .sameSite(SameSite.Lax),
        )
    }

    override fun resolveAuthRequest(request: Request): AuthRequest {
        val authRequestTrackingCookie = Cookies.required(cookieName)[request]

        val claims = authRequestTrackingCookie.value.parseJwt(secretKey)

        return AuthRequest(
            client = ClientId(claims[CLIENT_ID].toString()),
            redirectUri = Uri.of(claims[REDIRECT_URI].toString()),
            scopes = claims[SCOPES].toString().removeSurrounding("[", "]").trim().split(","),
            state = claims[STATE]?.toString()?.let { State(it) },
            responseType = ResponseType.valueOf(claims[RESPONSE_TYPE].toString()),
            responseMode = claims[RESPONSE_MODE]?.toString()?.let { ResponseMode.valueOf(it) },
        )
    }
}

private const val CLIENT_ID = "client_id"
private const val REDIRECT_URI = "redirect_uri"
private const val SCOPES = "scopes"
private const val STATE = "state"
private const val RESPONSE_TYPE = "response_type"
private const val RESPONSE_MODE = "response_mode"
