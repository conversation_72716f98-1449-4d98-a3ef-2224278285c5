plugins {
    id("hero.kotlin-run-service-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    implementation(projectModule(":Modules:BaseUtils"))
    implementation(projectModule(":Modules:Http4k"))
    implementation(projectModule(":Modules:Gjirafa"))
    implementation(projectModule(":Modules:GoogleCloud"))
    implementation(projectModule(":Modules:Jwt"))
    implementation(projectModule(":Modules:Model"))
    implementation(projectModule(":Modules:Exceptions"))
    implementation(projectModule(":Modules:Repository"))
    testImplementation(projectModule(":Modules:Testing"))
    testImplementation(projectModule(":Modules:IntegrationTesting"))
}
