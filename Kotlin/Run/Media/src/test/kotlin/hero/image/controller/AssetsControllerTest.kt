package hero.image.controller

import com.google.cloud.storage.BlobId
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.mockNow
import hero.baseutils.plusDays
import hero.http4k.auth.withAccessTokenCookie
import hero.jackson.fromJson
import hero.jackson.toJson
import hero.jwt.JwtUser
import hero.jwt.toJwt
import hero.media.controller.AssetsController
import hero.media.controller.dto.MediaUploadBody
import hero.media.controller.dto.StorageUploadSignedUrl
import hero.media.service.StorageUploadsService
import hero.model.StorageEntityType
import hero.model.StorageUploadFromUrlPostBody
import hero.model.StorageUploadResponse
import io.mockk.clearAllMocks
import org.http4k.core.Method
import org.http4k.core.Request
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

internal class AssetsControllerTest {
    private val production = false
    private val bucketName = "heroheroco-assets"
    private val storageUploadService = StorageUploadsService(SystemEnv.cloudProject, production)
    private val assetsController = AssetsController(storageUploadService)
    private val now = Instant.now().plusDays(1).truncatedTo(ChronoUnit.SECONDS)

    @BeforeEach
    fun beforeEach() {
        clearAllMocks()
        mockNow(now.toString())
    }

    @ParameterizedTest
    @ValueSource(strings = ["gif", "webp"])
    fun `image upload from url`(extension: String) {
        val user = JwtUser("tst-tst", Instant.now().plusDays(1).epochSecond, 0)
        val uploadRequest = StorageUploadFromUrlPostBody(
            targetEntityType = StorageEntityType.USER,
            sourceUrl = "https://dummyimage.com/100.$extension",
        )
        val request = Request(Method.POST, "/v1/uploads-from-url")
            .withAccessTokenCookie(user.toJwt())
            .body(uploadRequest.toJson())

        val response = assetsController.routeAssetFromUrl(request).bodyString().fromJson<StorageUploadResponse>()
        val expectedPath = "${production.envPrefix}/images/user/tst-tst/${now.epochSecond}-0-0000.$extension"
        assertEquals("https://$bucketName.storage.googleapis.com/$expectedPath", response.targetUrl)

        val blob = storageUploadService.storage.get(BlobId.of(bucketName, expectedPath))
        assertNotNull(blob)
        assertTrue(blob.exists())
        assertTrue(blob.delete())
    }

    @ParameterizedTest
    @ValueSource(strings = ["image/gif", "image/jpg"])
    fun `regular media upload`(contentType: String) {
        val suffix = contentType.replace("[a-z]+/".toRegex(), "")
        val user = JwtUser("tst-tst", Instant.now().plusDays(1).epochSecond, 0)
        val uploadRequest = MediaUploadBody(
            contentType = contentType,
            targetEntityType = StorageEntityType.POST,
            nonce = Instant.now().nano.toString(),
        )

        val request = Request(Method.POST, "/v1/uploads")
            .withAccessTokenCookie(user.toJwt())
            .header("Origin", "https://devel.herohero.co")
            .body(uploadRequest.toJson())

        val response = assetsController.routeAssetUploadUrl(request)
        val responseObject = response.bodyString().fromJson<StorageUploadSignedUrl>()
        if ("image" in contentType) {
            assertEquals(
                "https://heroheroco-assets.storage.googleapis.com/devel/images/post/tst-tst/" +
                    "${now.epochSecond}-0-0.$suffix",
                responseObject.id,
            )
            assertTrue(
                "https://storage.googleapis.com/heroheroco-assets/devel/images/post/tst-tst/" +
                    "${now.epochSecond}-0-0" in responseObject.uploadUrl,
            )
        } else {
            assertEquals("1577908800-0-0", responseObject.id)
            assertTrue(
                "https://storage.googleapis.com/heroheroco-media-uploads/devel/tst-tst/" +
                    "${now.epochSecond}-0-0.$suffix" in
                    responseObject.uploadUrl,
            )
        }
        val expectedPreviewUrl = when (suffix) {
            "gif" ->
                "https://heroheroco-assets.storage.googleapis.com/devel/images/post/tst-tst/" +
                    "${now.epochSecond}-0-0.gif"
            "jpg" -> "https://assets.herohero.co/devel/images/post/tst-tst/${now.epochSecond}-0-0.jpg"
            "mp4" -> null
            "m4a" -> null
            else -> error("Unexpected suffix: $suffix")
        }

        assertEquals(expectedPreviewUrl, responseObject.previewUrl)
    }
}
