package hero.media.service

import hero.gjirafa.dto.exampleGjirafaAsset
import hero.model.GjirafaStatus.COMPLETE
import hero.model.GjirafaStatus.PROCESSING
import hero.model.PostAsset
import hero.model.topics.PostState
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections.postsCollection
import hero.test.TestRepositories.postRepository
import hero.test.logging.TestLogger
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class GjirafaPostServiceIT : IntegrationTest() {
    @Test
    fun `should update post from processing to published and save both firestore and postgres`() {
        val underTest = GjirafaPostService(postsCollection, postRepository, pubSubMock, TestLogger)

        val processingAsset = exampleGjirafaAsset.copy(status = PROCESSING)
        val completeAsset = exampleGjirafaAsset.copy(status = COMPLETE)
        val post = testHelper.createPost(
            "cestmir",
            assets = listOf(PostAsset(gjirafa = processingAsset)),
            state = PostState.PROCESSING,
        )

        underTest.refreshAsset(post, completeAsset.id, completeAsset)

        val firestorePost = postsCollection[post.id].get()
        assertThat(firestorePost.state).isEqualTo(PostState.PUBLISHED)
        assertThat(firestorePost.assets).containsOnly(PostAsset(gjirafa = completeAsset))

        val postgresPost = postRepository.getById(post.id)
        assertThat(postgresPost.state).isEqualTo(PostState.PUBLISHED)
        assertThat(postgresPost.assets).containsOnly(PostAsset(gjirafa = completeAsset))
    }

    @Test
    fun `should keep post in processing state if asset is not compplete`() {
        val underTest = GjirafaPostService(postsCollection, postRepository, pubSubMock, TestLogger)

        val processingAsset = exampleGjirafaAsset.copy(status = PROCESSING)
        val post = testHelper.createPost(
            "cestmir",
            assets = listOf(PostAsset(gjirafa = processingAsset)),
            state = PostState.PROCESSING,
        )

        underTest.refreshAsset(post, processingAsset.id, processingAsset)

        val firestorePost = postsCollection[post.id].get()
        assertThat(firestorePost.state).isEqualTo(PostState.PROCESSING)
        assertThat(firestorePost.assets).containsOnly(PostAsset(gjirafa = processingAsset))

        val postgresPost = postRepository.getById(post.id)
        assertThat(postgresPost.state).isEqualTo(PostState.PROCESSING)
        assertThat(postgresPost.assets).containsOnly(PostAsset(gjirafa = processingAsset))
    }
}
