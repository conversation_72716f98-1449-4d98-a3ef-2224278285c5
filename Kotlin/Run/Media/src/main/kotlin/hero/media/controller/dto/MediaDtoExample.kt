package hero.media.controller.dto

import hero.model.StorageEntityType
import hero.model.StorageUploadFromUrlPostBody
import hero.model.StorageUploadResponse

val storageUploadResponseExample = StorageUploadResponse("https://uploaded")
val storageUploadFromUrlRequestExample = StorageUploadFromUrlPostBody(StorageEntityType.POST, "https://source")

val storageUploadSignedUrlExample = StorageUploadSignedUrl(
    id = "image-id",
    uploadUrl = "https://upload/via/put",
    previewUrl = "https://preview/image.webp",
)

val mediaUploadBodyExample = MediaUploadBody(
    contentType = "image/webp",
    targetEntityType = StorageEntityType.POST,
    nonce = "1234",
)
