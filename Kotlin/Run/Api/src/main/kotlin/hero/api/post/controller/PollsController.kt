package hero.api.post.controller

import hero.api.post.controller.dto.examplePollResponse
import hero.api.post.controller.dto.toResponse
import hero.api.post.service.GetPoll
import hero.api.post.service.PollQueryService
import hero.http4k.auth.getJwtUser
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path
import org.http4k.lens.string

class PollsController(
    private val pollQueryService: PollQueryService,
) {
    @Suppress("unused")
    val routeGetPoll: ContractRoute =
        ("/v1/polls" / Path.string().of("pollId")).get(
            summary = "Get poll by Id.",
            tag = "Polls",
            parameters = object {},
            responses = listOf(Status.OK example examplePollResponse),
            handler = { request, _, pollId ->
                val userId = request.getJwtUser().id

                val result = pollQueryService.execute(GetPoll(userId, pollId))

                Response(Status.OK).body(result.toResponse())
            },
        )
}
