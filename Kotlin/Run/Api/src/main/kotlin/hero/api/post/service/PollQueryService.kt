package hero.api.post.service

import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.gcloud.TypedCollectionReference
import hero.model.Poll
import hero.model.Subscriber
import hero.repository.post.JooqPollHelper
import hero.sql.jooq.Tables.POLL
import org.jooq.DSLContext

class PollQueryService(
    lazyContext: Lazy<DSLContext>,
    private val subscribersCollection: TypedCollectionReference<Subscriber>,
) {
    private val context: DSLContext by lazyContext

    fun execute(query: GetPoll): Poll {
        val pollRecord = context
            .select(JooqPollHelper.pollFields)
            .from(POLL)
            .where(POLL.ID.eq(query.pollId))
            .fetchOne()

        if (pollRecord == null) {
            throw NotFoundException("Poll ${query.pollId} not found")
        }

        val creatorId = pollRecord[POLL.USER_ID]
        val userId = query.userId
        if (userId != creatorId && subscribersCollection.fetchActiveSubscription(userId, creatorId) == null) {
            throw ForbiddenException(
                "User $userId does not subscribe $creatorId",
            )
        }

        return JooqPollHelper.mapRecordToEntity(pollRecord)
    }
}

data class GetPoll(val userId: String, val pollId: String)
