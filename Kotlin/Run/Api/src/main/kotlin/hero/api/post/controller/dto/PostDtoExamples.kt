package hero.api.post.controller.dto

import hero.api.post.service.CreatorPostsSortingFields
import hero.api.post.service.GetCreatorPostsFilter
import hero.api.post.service.PostFilterType
import hero.api.post.service.dto.GjirafaAssetInput
import hero.api.post.service.dto.GjirafaLivestreamAssetInput
import hero.api.post.service.dto.ImageAssetInput
import hero.api.post.service.dto.PostAssetInput
import hero.api.post.service.dto.PostInput
import hero.contract.api.dto.CategoryResponse
import hero.contract.api.dto.PagedPostResponse
import hero.contract.api.dto.PostRelationships
import hero.contract.api.dto.PostResponse
import hero.contract.api.dto.SavedCreatorPostInfoResponse
import hero.core.data.Sort
import hero.gjirafa.dto.exampleGjirafaAsset
import hero.gjirafa.dto.exampleGjirafaLiveAsset
import hero.model.Chapter
import hero.model.DocumentAsset
import hero.model.DocumentType
import hero.model.ImageAssetDto
import hero.model.PostAssetDto
import hero.model.PostCounts
import hero.model.YouTubeAsset
import hero.model.topics.PostState
import java.time.Instant

val examplePostResponse = PostResponse(
    id = "1683706670998-1431998399-1693903390616-axgzjkxarblzbkqftia",
    state = PostState.DELETED,
    counts = PostCounts(
        comments = 1,
        replies = 2,
    ),
    assets = listOf(
        PostAssetDto(
            image = ImageAssetDto("https://uploaded/image/url", 640, 480),
            youTube = YouTubeAsset("5laRP4DP1Sc", 640, 480, "https://thumbnail.jpg"),
            gjirafa = exampleGjirafaAsset,
            gjirafaLive = exampleGjirafaLiveAsset,
            document = DocumentAsset("https://uploaded/image/url", DocumentType.PDF, "super.pdf"),
            thumbnail = "https://uploaded/image/url",
            bunnyAsset = "https://bunny-asset",
            audioAsset = "https://audio-asset.mp3",
        ),
    ),
    assetsCount = 1,
    price = 10,
    fullAsset = false,
    pinnedAt = Instant.now(),
    publishedAt = Instant.now(),
    text = "text",
    textHtml = "<h1>text<h1>",
    excludeFromRss = false,
    textDelta = "text-delta",
    categories = listOf(
        CategoryResponse(
            id = "category-id",
            name = "category",
            slug = "category-slug",
        ),
    ),
    savedPostInfo = SavedCreatorPostInfoResponse("userId-postId", Instant.now()),
    relationships = PostRelationships(
        userId = "user1",
        messageThreadId = "1683706670998-1431998399",
        parentId = "parent-id",
        siblingId = "sibling-id",
    ),
    chapters = listOf(
        Chapter("first", 13),
        Chapter("second", 200),
    ),
    isAgeRestricted = true,
    isSponsored = true,
)

val exampleCommentResponse = CommentResponse(examplePostResponse, examplePostResponse, examplePostResponse)

val examplePaginatedPostResponse = PagedPostResponse(
    content = listOf(examplePostResponse),
    hasNext = false,
    afterCursor = "eyJsYXN0UHVibGlzaGVkQXQiOiIyMDIzLTA5LTA1VDA4OjQzOjEwLjYxNjQzMVoifQ==",
    beforeCursor = "eyJsYXN0UHVibGlzaGVkQXQiOiIyMDIzLTA5LTA1VDA4OjQzOjEwLjYxNjQzMVoifQ==",
)

val exampleSearchPostsRequest = SearchPostsRequest(
    creatorId = "creator-id",
    afterCursor = "after-cursor",
    beforeCursor = "before-cursor",
    pageSize = 10,
    filter = GetCreatorPostsFilter(type = PostFilterType.IN_PROGRESS, categoryId = "category-id", query = "query"),
    sortBy = CreatorPostsSortingFields.WATCHED_AT,
    sortDirection = Sort.Direction.DESC,
)

private val examplePostInput = PostInput(
    text = "text",
    textHtml = "<h1>cus</h1>",
    textDelta = "{\"ops\":[{\"insert\":\"dobrej komentar\\n\"}]}",
    assets = listOf(
        PostAssetInput(
            image = ImageAssetInput(
                url = "url",
                width = 100,
                height = 500,
                hidden = false,
            ),
            gjirafa = GjirafaAssetInput(id = "vjsnqrol"),
            gjirafaLivestream = GjirafaLivestreamAssetInput("vjsnvehf"),
            document = DocumentAsset(
                url = "url-document",
                type = DocumentType.PDF,
                name = "document-name",
            ),
            thumbnail = "thumbnail",
        ),
    ),
)

val exampleCreateCommentRequest = CreateCommentRequest(
    parentId = "parent-id",
    siblingId = "sibling-id",
    attributes = examplePostInput,
)

val exampleCreatePostRequest = CreatePostRequest(
    publishedAt = Instant.now(),
    categories = setOf("sports"),
    attributes = examplePostInput,
    isSponsored = true,
    isAgeRestricted = false,
)

val exampleUpdatePostRequest = UpdatePostRequest(
    publishedAt = Instant.now(),
    pinnedAt = Instant.now(),
    categories = setOf("sports"),
    attributes = examplePostInput,
    isSponsored = true,
    excludeFromRss = false,
    isAgeRestricted = false,
)

val exampleUpdateCommentRequest = UpdateCommentRequest(
    attributes = examplePostInput,
)

val examplePollResponse = PollResponse(
    id = "poll-id",
    deadline = Instant.now(),
    options = listOf(
        PollOptionResponse(
            id = "option-id",
            title = "Option 1",
            voteCount = 10,
        ),
        PollOptionResponse(
            id = "option-id-2",
            title = "Option 2",
            voteCount = 0,
        ),
    ),
)
