package hero.api.user.controller

import hero.http4k.extensions.lens
import hero.model.SearchCountryDtoV2Response
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Status
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * Might be flaky since the tests are dependent on 3rd party api
 */
class CountriesControllerTest {
    @Test
    fun `should correctly search for Czechia and return it's data`() {
        val underTest = CountriesController()
        val request = Request(Method.GET, "/v1/countries").query("query", "tchek")

        val response = underTest.routeSearchCountries(request)
        val searchCountryResponse = lens<SearchCountryDtoV2Response>().extract(response)

        assertEquals(Status.OK, response.status)
        assertTrue(searchCountryResponse.results.isNotEmpty())

        val firstCountryResult = searchCountryResponse.results.first()
        assertEquals("CZ", firstCountryResult.id)
        assertEquals("Czechia", firstCountryResult.attributes.translations["en"]?.common)
        assertEquals("Česko", firstCountryResult.attributes.translations["cs"]?.common)
    }

    @Test
    fun `should return all countries if query is not passed`() {
        val underTest = CountriesController()
        val request = Request(Method.GET, "/v1/countries")

        val response = underTest.routeSearchCountries(request)
        val searchCountryResponse = lens<SearchCountryDtoV2Response>().extract(response)

        assertTrue(searchCountryResponse.results.isNotEmpty())
    }

    @Test
    fun `should return empty list of results if nothing is found using the query`() {
        val underTest = CountriesController()
        val request = Request(Method.GET, "/v1/countries").query("query", "garbagequery")

        val response = underTest.routeSearchCountries(request)
        val searchCountryResponse = lens<SearchCountryDtoV2Response>().extract(response)

        assertTrue(searchCountryResponse.results.isEmpty())
    }
}
