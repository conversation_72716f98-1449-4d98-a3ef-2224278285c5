package hero.api.category.controller

import hero.api.category.controller.CategoriesController.CategoryOrdering
import hero.api.category.controller.CategoriesController.CategoryResponse
import hero.api.category.repository.CategoriesRepository
import hero.baseutils.SystemEnv
import hero.baseutils.mockNow
import hero.baseutils.plusDays
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.UnauthorizedException
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.http4k.auth.withAccessTokenCookie
import hero.http4k.extensions.fromJson
import hero.jackson.toJson
import hero.jwt.JwtUser
import hero.jwt.toJwt
import hero.model.Category
import hero.model.CategoryDto
import hero.model.CategoryDtoAttributes
import hero.model.CategoryDtoRelationship
import hero.model.CategoryDtoRelationships
import hero.model.Post
import hero.model.UserDtoRelationship
import io.mockk.clearAllMocks
import io.mockk.unmockkAll
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Response
import org.http4k.core.Status
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.Instant
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

internal class CategoriesControllerTest {
    @BeforeEach
    fun init() {
        mockNow("2020-01-01T20:00:00Z")
    }

    private val user = JwtUser("herherwetwnwakaabb", Instant.now().plusDays(1).epochSecond, 0)
    private val otherUser = JwtUser("otehrherherwetwnwakaabb", Instant.now().plusDays(1).epochSecond, 0)

    @AfterEach
    fun clear() {
        clearAllMocks()
        unmockkAll()

        val categoriesToDelete = categoriesCollection.where(Category::userId).isEqualTo(user.id).fetchAll()
        categoriesToDelete.forEach {
            categoriesCollection[it.id].delete()
        }
    }

    private val environment = "devel"

    private val firestore = firestore(SystemEnv.cloudProject, false)

    private val categoriesCollection = firestore.typedCollectionOf(Category)

    private val repository = CategoriesRepository(
        collection = categoriesCollection,
        postsCollection = firestore.typedCollectionOf(Post),
    )

    private val controller = CategoriesController(
        repository = repository,
    )

    private val newCategoryDto = CategoryDto(
        id = null,
        attributes = CategoryDtoAttributes(
            name = "Ny Kategori",
            slug = null,
            createdAt = null,
            postCount = 123,
        ),
        relationships = CategoryDtoRelationships(
            user = UserDtoRelationship(user.id),
        ),
    )

    @Test
    fun postGetPatchAndDelete() {
        val unauthorizedPostRequest = Request(Method.POST, "/v1/users/${user.id}/categories")
            .body(newCategoryDto.toJson())

        assertThrows<UnauthorizedException> {
            controller.routePostCategories(
                unauthorizedPostRequest
                    .withAccessTokenCookie(""),
            )
        }

        val authorizedPostRequest = unauthorizedPostRequest
            .withAccessTokenCookie(user.toJwt())

        val postResponse: Response = controller.routePostCategories(authorizedPostRequest)
        val createdCategoryDto = postResponse.fromJson<CategoryDto>()

        assertEquals("herherwetwnwakaabb-yiobrlqinbvjessg", createdCategoryDto.id)
        assertEquals("Ny Kategori", createdCategoryDto.attributes.name)
        assertEquals("ny-kategori", createdCategoryDto.attributes.slug)
        assertNotNull(createdCategoryDto.attributes.createdAt)

        controller.routeGetCategories(
            Request(Method.GET, "/v1/users/${user.id}/categories")
                .withAccessTokenCookie(""),
        )

        val getResponse = controller.routeGetCategories(
            Request(Method.GET, "/v1/users/${user.id}/categories")
                .withAccessTokenCookie(user.toJwt()),
        ).fromJson<CategoryResponse>()

        assertEquals(1, getResponse.categories.size)
        assertEquals(createdCategoryDto, getResponse.categories.first())

        val modifiedCategory = createdCategoryDto.copy(
            attributes = createdCategoryDto.attributes.copy(name = "Mycket Gammal Kategori"),
        )
        val unauthorizedPatchRequest = Request(Method.PATCH, "/v1/users/${user.id}/categories/${createdCategoryDto.id}")
            .body(modifiedCategory.toJson())

        assertThrows<UnauthorizedException> {
            controller.routePatchCategory(
                unauthorizedPatchRequest
                    .withAccessTokenCookie(""),
            )
        }

        assertThrows<ForbiddenException> {
            controller.routePatchCategory(
                unauthorizedPatchRequest
                    .withAccessTokenCookie(otherUser.toJwt()),
            )
        }

        val patchedCategoryDto = controller.routePatchCategory(
            unauthorizedPatchRequest
                .withAccessTokenCookie(user.toJwt()),
        ).fromJson<CategoryDto>()

        assertEquals("Mycket Gammal Kategori", patchedCategoryDto.attributes.name)
        assertEquals("mycket-gammal-kategori", patchedCategoryDto.attributes.slug)

        val unauthorizedDeleteRequest =
            Request(Method.DELETE, "/v1/users/${user.id}/categories/${createdCategoryDto.id}")

        val deleteResponse = controller.routeDeleteCategory(
            unauthorizedDeleteRequest
                .withAccessTokenCookie(user.toJwt()),
        )
        assertEquals(Status.NO_CONTENT, deleteResponse.status)

        val getResponseAfterDeletion = controller.routeGetCategories(
            Request(Method.GET, "/v1/users/${user.id}/categories")
                .withAccessTokenCookie(user.toJwt()),
        ).fromJson<CategoryResponse>()

        assertEquals(0, getResponseAfterDeletion.categories.size)
    }

    @Test
    fun postSortCategories() {
        val category0 = Category(
            id = "herherwetwnwakaabb-aaaa",
            name = "Ny Kategori-A",
            slug = "ny-kategori-a",
            createdAt = Instant.now(),
            postCount = 1,
            userId = user.id,
            index = 0,
        )
        val category1 = Category(
            id = "herherwetwnwakaabb-bbbb",
            name = "Ny Kategori-B",
            slug = "ny-kategori-b",
            createdAt = Instant.now(),
            postCount = 2,
            userId = user.id,
            index = 1,
        )
        val category2 = Category(
            id = "herherwetwnwakaabb-cccc",
            name = "Ny Kategori-C",
            slug = "ny-kategori-c",
            createdAt = Instant.now(),
            postCount = 3,
            userId = user.id,
            index = 2,
        )

        categoriesCollection[category0.id].set(category0)
        categoriesCollection[category1.id].set(category1)
        categoriesCollection[category2.id].set(category2)

        val newOrdering = CategoryOrdering(
            listOf(
                CategoryDtoRelationship(category2.id),
                CategoryDtoRelationship(category0.id),
                CategoryDtoRelationship(category1.id),
            ),
        )
        val unauthorizedPostRequest = Request(Method.POST, "/v1/users/${user.id}/categories-order")
            .body(newOrdering.toJson())

        assertThrows<UnauthorizedException> {
            controller.routePostCategoryOrder(
                unauthorizedPostRequest
                    .withAccessTokenCookie(""),
            )
        }

        val authorizedPostRequest = unauthorizedPostRequest
            .withAccessTokenCookie(user.toJwt())

        controller.routePostCategoryOrder(authorizedPostRequest)

        // read categories back from firestore and assert correct order
        assertEquals(0, categoriesCollection[category2.id].fetch()?.index)
        assertEquals(1, categoriesCollection[category0.id].fetch()?.index)
        assertEquals(2, categoriesCollection[category1.id].fetch()?.index)
    }
}
