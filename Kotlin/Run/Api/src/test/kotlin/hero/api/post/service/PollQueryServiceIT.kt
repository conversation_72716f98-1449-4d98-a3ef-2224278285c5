package hero.api.post.service

import hero.baseutils.truncated
import hero.exceptions.http.ForbiddenException
import hero.model.Poll
import hero.model.PollOption
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections.subscribersCollection
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class PollQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetPoll {
        @Test
        fun `should get the poll if the user is a subscriber`() {
            val underTest = PollQueryService(lazyTestContext, subscribersCollection)
            val poll = Poll(
                id = "poll-id",
                deadline = Instant.now(),
                options = listOf(PollOption(id = "option-id", title = "option 1", voteCount = 10)),
            )

            testHelper.createUser("cestmir")
            testHelper.createUser("filip")
            testHelper.createSubscriber("cestmir", "filip")

            testHelper.createPost("cestmir", poll = poll)

            val result = underTest.execute(GetPoll("filip", "poll-id"))

            assertThat(result).isEqualTo(poll)
        }

        @Test
        fun `should get the poll if the user is the owner`() {
            val underTest = PollQueryService(lazyTestContext, subscribersCollection)
            val poll = Poll(
                id = "poll-id",
                deadline = Instant.now().truncated(),
                options = listOf(PollOption(id = "option-id", title = "option 1", voteCount = 10)),
            )

            // no subscriber
            testHelper.createUser("cestmir")
            testHelper.createPost("cestmir", poll = poll)

            val result = underTest.execute(GetPoll("cestmir", "poll-id"))

            assertThat(result).isEqualTo(poll)
        }

        @Test
        fun `should throw forbidden if the user does not subscribe the creator`() {
            val underTest = PollQueryService(lazyTestContext, subscribersCollection)
            val poll = Poll(
                id = "poll-id",
                deadline = Instant.now().truncated(),
                options = listOf(PollOption(id = "option-id", title = "option 1", voteCount = 10)),
            )

            testHelper.createUser("cestmir")
            // missing subscription
            testHelper.createUser("filip")

            testHelper.createPost("cestmir", poll = poll)

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(GetPoll("filip", "poll-id"))
            }
        }
    }
}
