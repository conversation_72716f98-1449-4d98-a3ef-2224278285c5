package hero.api.post.service

import hero.api.post.service.dto.GjirafaAssetInput
import hero.api.post.service.dto.GjirafaLivestreamAssetInput
import hero.api.post.service.dto.PostAssetInput
import hero.baseutils.instantOf
import hero.baseutils.minus
import hero.baseutils.minusHours
import hero.baseutils.plusDays
import hero.baseutils.plusMinutes
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gjirafa.GjirafaLivestreamsService
import hero.gjirafa.GjirafaUploadsService
import hero.gjirafa.dto.LiveVideoResponseV2
import hero.model.GjirafaAsset
import hero.model.GjirafaLiveAsset
import hero.model.GjirafaStatus
import hero.model.LiveVideoStatus
import hero.model.PostAsset
import hero.model.topics.PostState
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import java.time.Instant
import kotlin.time.Duration.Companion.seconds
import hero.gjirafa.dto.LiveVideoStatus as GjirafaLiveVideoStatus

class PostServiceIT : IntegrationTest(mockInstantNow = true) {
    @Nested
    inner class UpdatePost {
        @Test
        fun `should update post's publishedAt, pinnedAt, text, textHtml, categories and price fields`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = PostService(
                TestCollections.postsCollection,
                TestRepositories.postRepository,
                gjirafaServiceMock,
                gjirafaLivestreamServiceMock,
                pubSubMock,
            )
            val now = Instant.now()
            val post = testHelper.createPost(
                "cestmir",
                text = "original text",
                textHtml = "original text html",
                createdAt = now - 5.seconds,
                updatedAt = now - 5.seconds,
            )

            val updatedPost = underTest.execute(
                UpdatePost(
                    postId = post.id,
                    userId = "cestmir",
                    publishedAt = instantOf("2024-03-08T12:10:00Z"),
                    pinnedAt = instantOf("2024-04-08T12:10:00Z"),
                    text = "updated text",
                    textHtml = "updated text html",
                    textDelta = "updated delta",
                    assets = listOf(),
                    price = 3L,
                    excludeFromRss = false,
                    categories = listOf("sports"),
                    postValidator = {},
                    isSponsored = true,
                    isAgeRestricted = true,
                ),
            )

            assertThat(updatedPost).isEqualTo(TestCollections.postsCollection[post.id].get())
            assertThat(updatedPost.text).isEqualTo("updated text")
            assertThat(updatedPost.textHtml).isEqualTo("updated text html")
            assertThat(updatedPost.textDelta).isEqualTo("updated delta")
            assertThat(updatedPost.published).isEqualTo(instantOf("2024-03-08T12:10:00Z"))
            assertThat(updatedPost.pinnedAt).isEqualTo(instantOf("2024-04-08T12:10:00Z"))
            assertThat(updatedPost.categories).isEqualTo(listOf("sports"))
            assertThat(updatedPost.price).isEqualTo(3L)

            assertThat(updatedPost.updated).isNotEqualTo(post.updated)
            assertThat(updatedPost.updated).isAfter(updatedPost.created)

            verify {
                pubSubMock.publish<PostStateChanged>(
                    match {
                        it.post == updatedPost && it.stateChange == PostStateChange.PATCHED
                    },
                )
            }
        }

        @Test
        fun `should not change post state from published to processing even if gjirafa assets are not ready`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>().apply {
                every { getAsset(userId = any(), assetId = any()) } returns notCompleteGjirafaAsset
            }
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = PostService(
                TestCollections.postsCollection,
                TestRepositories.postRepository,
                gjirafaServiceMock,
                gjirafaLivestreamServiceMock,
                pubSubMock,
            )
            val post = testHelper.createPost("cestmir", state = PostState.PUBLISHED)

            val updatedPost = underTest.execute(
                UpdatePost(
                    postId = post.id,
                    userId = "cestmir",
                    text = "updated text",
                    textHtml = "updated text html",
                    textDelta = "text-delta",
                    assets = listOf(PostAssetInput(gjirafa = GjirafaAssetInput("vjsnqrol"))),
                    isSponsored = true,
                    isAgeRestricted = true,
                ),
            )

            assertThat(updatedPost.state).isEqualTo(PostState.PUBLISHED)
            verify(exactly = 1) { gjirafaServiceMock.getAsset(userId = "cestmir", assetId = "vjsnqrol") }
        }

        @Test
        fun `should update a post with a livestream`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>().apply {
                every { getLiveVideo(any()) } returns liveVideoResponseV2
            }
            val underTest = PostService(
                TestCollections.postsCollection,
                TestRepositories.postRepository,
                gjirafaServiceMock,
                gjirafaLivestreamServiceMock,
                pubSubMock,
            )
            val post = testHelper.createPost("cestmir", state = PostState.PUBLISHED)

            val updatedPost = underTest.execute(
                UpdatePost(
                    postId = post.id,
                    userId = "cestmir",
                    text = "updated text",
                    textHtml = "updated text html",
                    textDelta = "updated text delta",
                    assets = listOf(PostAssetInput(gjirafaLivestream = GjirafaLivestreamAssetInput("vjsnqrol"))),
                    isSponsored = true,
                    isAgeRestricted = true,
                ),
            )

            assertThat(updatedPost.assets).isEqualTo(
                listOf(
                    PostAsset(
                        gjirafaLive = GjirafaLiveAsset(
                            id = "vjsnqrol",
                            playbackUrl = "playback-url",
                            channelPublicId = "channel-public-id",
                            liveStatus = LiveVideoStatus.LIVE,
                        ),
                    ),
                ),
            )
            verify(exactly = 1) { gjirafaLivestreamServiceMock.getLiveVideo("vjsnqrol") }
        }

        @Test
        fun `should update post state from scheduled to processing if gjirafa assets are not ready`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>().apply {
                every { getAsset(userId = any(), assetId = any()) } returns notCompleteGjirafaAsset
            }
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = PostService(
                TestCollections.postsCollection,
                TestRepositories.postRepository,
                gjirafaServiceMock,
                gjirafaLivestreamServiceMock,
                pubSubMock,
            )
            val post = testHelper.createPost("cestmir", state = PostState.SCHEDULED)

            // publishedAt is in the future, but gjirafa assets are not ready, so the state must be processing
            val updatedPost = underTest.execute(
                UpdatePost(
                    postId = post.id,
                    userId = "cestmir",
                    text = "updated text",
                    textHtml = "updated text html",
                    textDelta = "updated text delta",
                    publishedAt = Instant.now().plusDays(1),
                    assets = listOf(PostAssetInput(gjirafa = GjirafaAssetInput("vjsnqrol"))),
                    isAgeRestricted = true,
                    isSponsored = true,
                ),
            )

            assertThat(updatedPost.state).isEqualTo(PostState.PROCESSING)
            verify(exactly = 1) { gjirafaServiceMock.getAsset(userId = "cestmir", assetId = "vjsnqrol") }
        }

        @Test
        fun `should keep state as processing if gjirafa assets are not ready`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>().apply {
                every { getAsset(userId = any(), assetId = any()) } returns notCompleteGjirafaAsset
            }
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = PostService(
                TestCollections.postsCollection,
                TestRepositories.postRepository,
                gjirafaServiceMock,
                gjirafaLivestreamServiceMock,
                pubSubMock,
            )
            val post = testHelper.createPost("cestmir", state = PostState.PROCESSING)

            val updatedPost = underTest.execute(
                UpdatePost(
                    postId = post.id,
                    userId = "cestmir",
                    text = "updated text",
                    textHtml = "updated text html",
                    textDelta = "updated text delta",
                    publishedAt = Instant.now().plusDays(1),
                    assets = listOf(PostAssetInput(gjirafa = GjirafaAssetInput("vjsnqrol"))),
                    isAgeRestricted = true,
                    isSponsored = true,
                ),
            )

            assertThat(updatedPost.state).isEqualTo(PostState.PROCESSING)
            verify(exactly = 1) { gjirafaServiceMock.getAsset(userId = "cestmir", assetId = "vjsnqrol") }
        }

        @Test
        fun `only post owner is allowed to update his posts`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = PostService(
                TestCollections.postsCollection,
                TestRepositories.postRepository,
                gjirafaServiceMock,
                gjirafaLivestreamServiceMock,
                pubSubMock,
            )
            val post = testHelper.createPost("cestmir")

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(
                    UpdatePost(
                        postId = post.id,
                        userId = "not-cestmir",
                        text = "updated text",
                        textHtml = "updated text html",
                        textDelta = "updated text delta",
                        assets = listOf(),
                        isSponsored = true,
                        isAgeRestricted = true,
                    ),
                )
            }
        }

        @Test
        fun `either text or assets must be provided`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = PostService(
                TestCollections.postsCollection,
                TestRepositories.postRepository,
                gjirafaServiceMock,
                gjirafaLivestreamServiceMock,
                pubSubMock,
            )
            val post = testHelper.createPost("cestmir")

            assertThatExceptionOfType(BadRequestException::class.java).isThrownBy {
                underTest.execute(
                    UpdatePost(
                        postId = post.id,
                        userId = "not-cestmir",
                        text = "",
                        textHtml = "",
                        textDelta = null,
                        assets = listOf(),
                        isSponsored = true,
                        isAgeRestricted = true,
                    ),
                )
            }
        }

        @ParameterizedTest
        @EnumSource(PostState::class, names = ["DELETED", "REVISION"])
        fun `deleted or revision posts cannot be updated`(postState: PostState) {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = PostService(
                TestCollections.postsCollection,
                TestRepositories.postRepository,
                gjirafaServiceMock,
                gjirafaLivestreamServiceMock,
                pubSubMock,
            )
            val post = testHelper.createPost("cestmir", state = postState)

            assertThatExceptionOfType(BadRequestException::class.java).isThrownBy {
                underTest.execute(
                    UpdatePost(
                        postId = post.id,
                        userId = "not-cestmir",
                        text = "post text",
                        textHtml = "",
                        textDelta = null,
                        assets = listOf(),
                        isSponsored = true,
                        isAgeRestricted = true,
                    ),
                )
            }
        }
    }
}

private val notCompleteGjirafaAsset = GjirafaAsset(
    key = "encode-key",
    audioByteSize = 0,
    audioStaticUrl = "audio-url",
    audioStreamUrl = "audio-stream",
    chaptersVttUrl = "chapters-vtt-url",
    duration = 0.0,
    hasAudio = true,
    hasVideo = true,
    hidden = false,
    id = "id",
    status = GjirafaStatus.ERROR,
    encodingFinishTime = null,
    encodingRemainingTime = null,
    createdAt = Instant.now().minusHours(1),
    projectId = "project-id",
    imageKey = "123456789",
)

private val liveVideoResponseV2 = LiveVideoResponseV2(
    id = "live-id",
    title = "live-video",
    description = null,
    playbackUrl = "playback-url",
    thumbnail = "thumbnail",
    channelPublicId = "channel-public-id",
    channelName = null,
    channelTitle = null,
    liveStatus = GjirafaLiveVideoStatus.LIVE,
    author = "author",
    publishDate = Instant.now().minusHours(1),
    publishEndDate = Instant.now().plusMinutes(1),
    startDateUTC = Instant.now().minusHours(2),
    canCutAndPublish = false,
    canStopAndCut = false,
)
