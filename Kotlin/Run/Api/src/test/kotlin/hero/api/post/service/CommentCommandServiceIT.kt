package hero.api.post.service

import hero.api.post.service.dto.ImageAssetInput
import hero.api.post.service.dto.PostAssetInput
import hero.api.post.service.dto.PostInput
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gjirafa.GjirafaLivestreamsService
import hero.gjirafa.GjirafaUploadsService
import hero.model.ImageAsset
import hero.model.PostAsset
import hero.model.PostCounts
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import hero.test.logging.TestLogger
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class CommentCommandServiceIT : IntegrationTest(mockInstantNow = true) {
    @Nested
    inner class CreateComment {
        @Test
        fun `should create comment with given attributes`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = CommentCommandService(
                TestCollections.postsCollection,
                PostService(
                    TestCollections.postsCollection,
                    TestRepositories.postRepository,
                    gjirafaServiceMock,
                    gjirafaLivestreamServiceMock,
                    pubSubMock,
                ),
                TestCollections.subscribersCollection,
                lazyTestContext,
                TestLogger,
            )
            testHelper.createUser("pavel")
            testHelper.createSubscriber("cestmir", "pavel")
            val post = testHelper.createPost("cestmir", counts = PostCounts(comments = 1))
            val comment = testHelper.createPost("filip", parentId = post.id)
            val assets = listOf(PostAssetInput(image = ImageAssetInput("http://picture.com", 100, 200)))
            val attributes = PostInput(text = "text", textHtml = "<p>a</p>", assets = assets)

            val result = underTest.execute(CreateComment("pavel", comment.id, "sibling-id", attributes))

            with(result) {
                assertThat(this).isEqualTo(TestCollections.postsCollection[result.id].get())
                assertThat(text).isEqualTo("text")
                assertThat(userId).isEqualTo("pavel")
                assertThat(messageThreadId).isNull()
                assertThat(notifiedAt).isNull()
                assertThat(textHtml).isEqualTo("<p>a</p>")
                assertThat(parentPostId).isEqualTo(post.id)
                assertThat(parentUserId).isEqualTo("cestmir")
                assertThat(parentId).isEqualTo(comment.id)
                assertThat(siblingId).isEqualTo("sibling-id")
                assertThat(published).isBetween(Instant.now().minusSeconds(5), Instant.now())
                assertThat(price).isNull()
                assertThat(this.assets).containsExactly(PostAsset(ImageAsset("http://picture.com", 100, 200)))
                assertThat(categories).isEmpty()
                assertThat(excludeFromRss).isFalse()
                assertThat(participingUserIds).isEmpty()
            }
        }

        @Test
        fun `should create new comment and increment post's counts`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = CommentCommandService(
                TestCollections.postsCollection,
                PostService(
                    TestCollections.postsCollection,
                    TestRepositories.postRepository,
                    gjirafaServiceMock,
                    gjirafaLivestreamServiceMock,
                    pubSubMock,
                ),
                TestCollections.subscribersCollection,
                lazyTestContext,
                TestLogger,
            )
            testHelper.createUser("pavel")
            testHelper.createSubscriber("cestmir", "pavel")
            val anotherPost = testHelper.createPost("cestmir")
            val post = testHelper.createPost("cestmir")
            val attributes = PostInput(text = "text", textHtml = "<p>text</p>")

            val result = underTest.execute(CreateComment("pavel", post.id, null, attributes))

            with(result) {
                assertThat(parentId).isEqualTo(post.id)
                assertThat(parentPostId).isEqualTo(post.id)
                assertThat(parentUserId).isEqualTo("cestmir")
            }

            // verify that post's comment count are correctly incremented in firestore
            with(post.id) {
                val firestoreComment = TestCollections.postsCollection[this].get()
                assertThat(firestoreComment.counts.comments).isEqualTo(1)
                assertThat(firestoreComment.counts.replies).isEqualTo(0)

                val postgresComment = TestRepositories.postRepository.getById(this)
                assertThat(postgresComment.counts.comments).isEqualTo(1)
                assertThat(postgresComment.counts.replies).isEqualTo(0)
            }

            // verify that other post has unchanged counts
            with(TestRepositories.postRepository.getById(anotherPost.id)) {
                assertThat(counts.comments).isEqualTo(0)
                assertThat(counts.replies).isEqualTo(0)
            }
        }

        @Test
        fun `should create a reply to a comment and increment comment's and post's counts`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = CommentCommandService(
                TestCollections.postsCollection,
                PostService(
                    TestCollections.postsCollection,
                    TestRepositories.postRepository,
                    gjirafaServiceMock,
                    gjirafaLivestreamServiceMock,
                    pubSubMock,
                ),
                TestCollections.subscribersCollection,
                lazyTestContext,
                TestLogger,
            )
            testHelper.createUser("pavel")
            testHelper.createSubscriber("cestmir", "pavel")
            val anotherPost = testHelper.createPost("cestmir")
            val post = testHelper.createPost("cestmir", counts = PostCounts(comments = 1))
            val comment = testHelper.createPost("filip", parentId = post.id)
            val attributes = PostInput(text = "text", textHtml = "<p>a</p>")

            val result = underTest.execute(CreateComment("pavel", comment.id, "sibling-id", attributes))

            with(result) {
                assertThat(parentId).isEqualTo(comment.id)
                assertThat(parentPostId).isEqualTo(post.id)
                assertThat(parentUserId).isEqualTo("cestmir")
            }

            // verify that post's reply count was incremented both in postgres and firestore
            with(post.id) {
                val firestoreComment = TestCollections.postsCollection[this].get()
                assertThat(firestoreComment.counts.comments).isEqualTo(1)
                assertThat(firestoreComment.counts.replies).isEqualTo(1)

                val postgresComment = TestRepositories.postRepository.getById(this)
                assertThat(postgresComment.counts.comments).isEqualTo(1)
                assertThat(postgresComment.counts.replies).isEqualTo(1)
            }

            // verify that comment's comment count was incremented both in postgres and firestore
            with(comment.id) {
                val firestoreComment = TestCollections.postsCollection[this].get()
                assertThat(firestoreComment.counts.comments).isEqualTo(1)
                assertThat(firestoreComment.counts.replies).isEqualTo(0)

                val postgresComment = TestRepositories.postRepository.getById(this)
                assertThat(postgresComment.counts.comments).isEqualTo(1)
                assertThat(postgresComment.counts.replies).isEqualTo(0)
            }

            // verify that other post has unchanged counts
            with(TestRepositories.postRepository.getById(anotherPost.id)) {
                assertThat(counts.comments).isEqualTo(0)
                assertThat(counts.replies).isEqualTo(0)
            }
        }

        @Test
        fun `only subscribers of post author can comment, others are forbidden`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = CommentCommandService(
                TestCollections.postsCollection,
                PostService(
                    TestCollections.postsCollection,
                    TestRepositories.postRepository,
                    gjirafaServiceMock,
                    gjirafaLivestreamServiceMock,
                    pubSubMock,
                ),
                TestCollections.subscribersCollection,
                lazyTestContext,
                TestLogger,
            )

            val post = testHelper.createPost("cestmir", counts = PostCounts(comments = 1))

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                val attributes = PostInput(text = "", textHtml = "")
                underTest.execute(CreateComment("pavel", post.id, null, attributes))
            }
        }

        @Test
        fun `only two levels of comments are allowed`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = CommentCommandService(
                TestCollections.postsCollection,
                PostService(
                    TestCollections.postsCollection,
                    TestRepositories.postRepository,
                    gjirafaServiceMock,
                    gjirafaLivestreamServiceMock,
                    pubSubMock,
                ),
                TestCollections.subscribersCollection,
                lazyTestContext,
                TestLogger,
            )
            testHelper.createUser("cestmir")
            testHelper.createSubscriber("cestmir", "pavel")
            val post = testHelper.createPost("cestmir", counts = PostCounts(comments = 1))
            val comment = testHelper.createPost("filip", parentId = post.id)
            val reply = testHelper.createPost("cung", parentId = comment.id)
            val attributes = PostInput(text = "text", textHtml = "<p>a</p>")

            assertThatExceptionOfType(BadRequestException::class.java).isThrownBy {
                underTest.execute(CreateComment("pavel", reply.id, null, attributes))
            }
        }
    }
}
