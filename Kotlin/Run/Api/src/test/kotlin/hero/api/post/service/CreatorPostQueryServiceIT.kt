package hero.api.post.service

import hero.api.post.service.CreatorPostsSortingFields.PUBLISHED_AT
import hero.api.post.service.CreatorPostsSortingFields.QUERY_SIMILARITY
import hero.api.post.service.CreatorPostsSortingFields.VIEWS
import hero.api.post.service.CreatorPostsSortingFields.WATCHED_AT
import hero.api.user.service.gjirafaAsset
import hero.baseutils.minus
import hero.baseutils.plus
import hero.core.data.PageRequest
import hero.core.data.Sort
import hero.core.data.Sort.Direction.ASC
import hero.core.data.Sort.Direction.DESC
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.NotFoundException
import hero.model.GjirafaLiveAsset
import hero.model.LiveVideoStatus
import hero.model.PostAsset
import hero.model.SubscriberStatus
import hero.model.topics.PostState
import hero.model.topics.PostState.DELETED
import hero.model.topics.PostState.PROCESSING
import hero.model.topics.PostState.PUBLISHED
import hero.model.topics.PostState.SCHEDULED
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.NullSource
import org.junit.jupiter.params.provider.ValueSource
import java.time.Instant
import java.time.LocalDate
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.seconds

class CreatorPostQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetCreatorLivestreams {
        @Test
        fun `should fetch only posts that have livestream asset with state set to LIVE`() {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )

            // normal gjirafa post
            testHelper.createPost("pablo", assets = listOf(PostAsset(gjirafa = gjirafaAsset)))

            // livestream post but the status is offline
            testHelper.createPost(
                "pablo",
                assets = listOf(
                    PostAsset(
                        gjirafaLive = GjirafaLiveAsset(
                            id = "offline-id",
                            playbackUrl = "playback-url",
                            channelPublicId = "channel-public-id",
                            liveStatus = LiveVideoStatus.OFFLINE,
                        ),
                    ),
                ),
            )

            // live livestream but the post is deleted
            testHelper.createPost(
                "pablo",
                state = DELETED,
                assets = listOf(
                    PostAsset(
                        gjirafaLive = GjirafaLiveAsset(
                            id = "live-id",
                            playbackUrl = "playback-url",
                            channelPublicId = "channel-public-id",
                            liveStatus = LiveVideoStatus.LIVE,
                        ),
                    ),
                ),
            )

            val livestreamPost = testHelper.createPost(
                "pablo",
                assets = listOf(
                    PostAsset(
                        gjirafaLive = GjirafaLiveAsset(
                            id = "live-id",
                            playbackUrl = "playback-url",
                            channelPublicId = "channel-public-id",
                            liveStatus = LiveVideoStatus.LIVE,
                        ),
                    ),
                ),
            )

            val livestreamPosts = underTest.execute(GetCreatorLivestreams("pablo"))

            assertThat(livestreamPosts.content).containsExactly(livestreamPost)
        }
    }

    @Nested
    inner class GetCreatorPostDetails {
        @Test
        fun `should fetch post details and subscription info and saved post info`() {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )
            val category = testHelper.createCategory("pablo")
            val creatorPost = testHelper.createPost("pablo", categories = listOf(category.id))
            val subscriber = testHelper.createSubscriber("pablo", "lemur", status = SubscriberStatus.ACTIVE)
            val savedPost = testHelper.createSavedPost("lemur", creatorPost)
            // saved post of another user
            testHelper.createSavedPost("fanda", creatorPost)

            val result = underTest.execute(GetCreatorPostDetails("lemur", creatorPost.id))

            assertThat(result.post).isEqualTo(creatorPost)
            assertThat(result.subscriptionInfo).isEqualTo(subscriber)
            assertThat(result.categories).containsExactly(category)
            assertThat(result.savedPostInfo).isEqualTo(SavedCreatorPostInfo(savedPost.id, savedPost.savedAt))
        }

        @ParameterizedTest
        @NullSource
        @ValueSource(strings = ["lemur"])
        fun `should fetch post details and with empty subscription info`(userId: String?) {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )
            val creatorPost = testHelper.createPost("pablo")

            val result = underTest.execute(GetCreatorPostDetails(userId, creatorPost.id))

            assertThat(result.post).isEqualTo(creatorPost)
            assertThat(result.subscriptionInfo).isNull()
        }

        @ParameterizedTest
        @EnumSource(PostState::class, names = ["DELETED", "REVISION"])
        fun `should throw not found if post is deleted or is a revision`(postState: PostState) {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )
            val creatorPost = testHelper.createPost("pablo", state = postState)

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.execute(GetCreatorPostDetails("lemur", creatorPost.id))
            }
        }

        @ParameterizedTest
        @EnumSource(PostState::class, names = ["SCHEDULED", "PROCESSING"])
        fun `should throw forbidden if post is processing, scheduled and user is not the owner`(postState: PostState) {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )
            val creatorPost = testHelper.createPost("pablo", state = postState)

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(GetCreatorPostDetails("lemur", creatorPost.id))
            }
        }

        @ParameterizedTest
        @EnumSource(PostState::class, names = ["SCHEDULED", "PROCESSING", "PUBLISHED"])
        fun `owner should be able to fetch post in all eligible states`(postState: PostState) {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )
            val creatorPost = testHelper.createPost("pablo", state = postState)

            assertDoesNotThrow {
                underTest.execute(GetCreatorPostDetails("pablo", creatorPost.id))
            }
        }

        @Test
        fun `should fetch post details and saved post info for my own post`() {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )
            val category = testHelper.createCategory("pablo")
            val creatorPost = testHelper.createPost("pablo", categories = listOf(category.id))
            val savedPost = testHelper.createSavedPost("pablo", creatorPost)

            val result = underTest.execute(GetCreatorPostDetails("pablo", creatorPost.id))

            assertThat(result.post).isEqualTo(creatorPost)
            assertThat(result.categories).containsExactly(category)
            assertThat(result.savedPostInfo).isEqualTo(SavedCreatorPostInfo(savedPost.id, savedPost.savedAt))
        }
    }

    @Nested
    inner class GetCreatorPosts {
        @Test
        fun `should fetch subscription info if user is a subscriber`() {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )
            val userSubscription = testHelper.createSubscriber("pablo", "lemur", status = SubscriberStatus.ACTIVE)
            val post = testHelper.createPost("pablo")

            val result = underTest.execute(GetCreatorPosts("lemur", "pablo", PageRequest()))

            assertThat(result.content)
                .containsExactly(CreatorPostWithSubscriptionInfo(post, listOf(), userSubscription))
        }

        @Test
        fun `subscription info should be null if user is a not a subscriber`() {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )
            val post = testHelper.createPost("pablo")

            val result = underTest.execute(GetCreatorPosts("lemur", "pablo", PageRequest()))

            assertThat(result.content)
                .containsExactly(CreatorPostWithSubscriptionInfo(post, listOf(), null))
        }

        @Test
        fun `should fetch saved post info if user has saved given post`() {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )

            val now = Instant.now()
            val post1 = testHelper.createPost("pablo", publishedAt = now)
            val post2 = testHelper.createPost("pablo", publishedAt = now - 1.days)

            val savedPost2 = testHelper.createSavedPost("lemur", post2)

            val result = underTest.execute(GetCreatorPosts("lemur", "pablo", PageRequest()))

            assertThat(result.content)
                .containsExactly(
                    CreatorPostWithSubscriptionInfo(post1, listOf(), null, null),
                    CreatorPostWithSubscriptionInfo(
                        post2,
                        listOf(),
                        null,
                        SavedCreatorPostInfo(savedPost2.id, savedPost2.savedAt),
                    ),
                )
        }

        @Test
        fun `author should be able to see his states that are both in PROCESSING, PUBLISHED and SCHEDULED states`() {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )

            val now = Instant.now()
            val publishedPost = testHelper.createPost("pablo", state = PUBLISHED, publishedAt = now)
            val processingPost = testHelper.createPost("pablo", state = PROCESSING, publishedAt = now - 5.seconds)
            val scheduledPost = testHelper.createPost("pablo", state = SCHEDULED, publishedAt = now + 10.seconds)

            val result = underTest.execute(GetCreatorPosts("pablo", "pablo", PageRequest()))

            assertThat(result.content.map { it.post })
                .containsExactly(scheduledPost, publishedPost, processingPost)
        }

        @Test
        fun `subscriber should be able to see only PUBLISHED posts`() {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )

            val now = Instant.now()
            val publishedPost = testHelper.createPost("pablo", state = PUBLISHED, publishedAt = now)
            val processingPost = testHelper.createPost("pablo", state = PROCESSING, publishedAt = now - 5.seconds)
            val scheduledPost = testHelper.createPost("pablo", state = SCHEDULED, publishedAt = now + 10.seconds)

            val result = underTest.execute(GetCreatorPosts("lemy", "pablo", PageRequest()))

            assertThat(result.content.map { it.post })
                .containsExactly(publishedPost)
                .doesNotContain(processingPost)
                .doesNotContain(scheduledPost)
        }

        @Test
        fun `should fetch only posts with given category`() {
            val underTest = CreatorPostQueryService(
                TestCollections.subscribersCollection,
                TestCollections.categoriesCollection,
                TestCollections.savedPostsCollection,
                TestRepositories.postRepository,
                lazyTestContext,
            )

            val sportCategory = testHelper.createCategory("pablo", name = "sport")
            val musicCategory = testHelper.createCategory("pablo", name = "music")
            val postWithCategory = testHelper.createPost(
                "pablo",
                categories = listOf(sportCategory.id, musicCategory.id),
            )

            // post without categories
            testHelper.createPost("pablo", categories = listOf())

            val result = underTest.execute(
                GetCreatorPosts(
                    "lemy",
                    "pablo",
                    PageRequest(),
                    GetCreatorPostsFilter(categoryId = sportCategory.id),
                ),
            )

            assertThat(result.content)
                .containsExactly(
                    CreatorPostWithSubscriptionInfo(
                        postWithCategory,
                        listOf(sportCategory, musicCategory),
                        null,
                    ),
                )
        }
    }

    @Nested
    inner class GetCreatorPostsSorting {
        @Nested
        inner class GetCreatorPostSortingPinnedAt {
            @Test
            fun `should page posts with pinned first, then unpinned sorted by their respective date fields`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val pinnedPost1 = testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val pinnedPost2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 2.days)

                val post1 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 2.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                val firstPinned = underTest.execute(GetCreatorPosts("lemur", "pablo", PageRequest(pageSize = 1)))
                assertThat(firstPinned.content.map { it.post })
                    .containsExactly(pinnedPost1)
                assertThat(firstPinned.hasNext).isTrue()

                val secondPinned = underTest.execute(GetCreatorPosts("lemur", "pablo", firstPinned.nextPageable))
                assertThat(secondPinned.content.map { it.post })
                    .containsExactly(pinnedPost2)

                val unpinnedPost1 = underTest.execute(GetCreatorPosts("lemur", "pablo", secondPinned.nextPageable))
                assertThat(unpinnedPost1.content.map { it.post })
                    .containsExactly(post2)

                val unpinnedPost2 = underTest.execute(GetCreatorPosts("lemur", "pablo", unpinnedPost1.nextPageable))
                assertThat(unpinnedPost2.content.map { it.post })
                    .containsExactly(post1)
                assertThat(unpinnedPost2.nextPageable.afterCursor).isNotNull()
                assertThat(unpinnedPost2.hasNext).isFalse()
            }

            @Test
            fun `should sort posts with pinned first by pinnedAt, then unpinned by publishedAt`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val pinnedPost1 = testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val pinnedPost2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 2.days)

                val post1 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 2.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 1.days)

                val result = underTest.execute(GetCreatorPosts("lemur", "pablo", PageRequest()))

                assertThat(result.content.map { it.post })
                    .containsExactly(pinnedPost1, pinnedPost2, post2, post1)
                assertThat(result.nextPageable.afterCursor).isNotNull()
                assertThat(result.hasNext).isFalse()
            }

            @Test
            fun `should fetch first pinned post(pinnedPost1) and then the rest of posts`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val pinnedPost1 = testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val pinnedPost2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 2.days)

                val post1 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 2.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 1.days)

                // we first fetch only the first pinned post
                val firstPinnedPost = underTest.execute(GetCreatorPosts("lemur", "pablo", PageRequest(pageSize = 1)))
                assertThat(firstPinnedPost.content.map { it.post })
                    .containsExactly(pinnedPost1)
                assertThat(firstPinnedPost.hasNext).isTrue()

                // then we fetch the second pinned post and rest of unpinned posts
                val nextPageable = PageRequest(afterCursor = firstPinnedPost.nextPageable.afterCursor)
                val restOfPosts = underTest.execute(GetCreatorPosts("lemur", "pablo", nextPageable))
                assertThat(restOfPosts.content.map { it.post })
                    .containsExactly(pinnedPost2, post2, post1)
                assertThat(restOfPosts.nextPageable.afterCursor).isNotNull()
                assertThat(restOfPosts.hasNext).isFalse()
            }

            @Test
            fun `should fetch all pinned posts first and then all unpinned posts`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val pinnedPost1 = testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val pinnedPost2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 2.days)

                val post1 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 2.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 1.days)

                // we fetch all of pinned posts(there are two)
                val allPinnedPosts = underTest.execute(GetCreatorPosts("lemur", "pablo", PageRequest(pageSize = 2)))
                assertThat(allPinnedPosts.content.map { it.post })
                    .containsExactly(pinnedPost1, pinnedPost2)
                assertThat(allPinnedPosts.hasNext).isTrue()

                // then we fetch the rest, which are the two unpinned posts
                val nextPageable = PageRequest(afterCursor = allPinnedPosts.nextPageable.afterCursor)
                val restOfPosts = underTest.execute(GetCreatorPosts("lemur", "pablo", nextPageable))
                assertThat(restOfPosts.content.map { it.post })
                    .containsExactly(post2, post1)
                assertThat(restOfPosts.nextPageable.afterCursor).isNotNull()
                assertThat(restOfPosts.hasNext).isFalse()
            }

            @Test
            fun `should sort correctly if there are only unpinned posts`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val post1 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 2.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 1.days)

                val result = underTest.execute(GetCreatorPosts("lemur", "pablo", PageRequest()))

                assertThat(result.content.map { it.post })
                    .containsExactly(post2, post1)
                    .allMatch { it.pinnedAt == null }
                assertThat(result.nextPageable.afterCursor).isNotNull()
                assertThat(result.hasNext).isFalse()
            }
        }

        @Nested
        inner class GetCreatorPostSortingPublishedAt {
            @Test
            fun `should page fwd and bck correctly and sort by published at desc, pinned at is ignored in this case`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val post1 = testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 3.days)

                val post3 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(PUBLISHED_AT, direction = DESC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post4)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post3)

                val page3 = underTest.execute(GetCreatorPosts("lemur", "pablo", page2.nextPageable))
                assertThat(page3.content.map { it.post })
                    .containsExactly(post2)

                val page4 = underTest.execute(GetCreatorPosts("lemur", "pablo", page3.nextPageable))
                assertThat(page4.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page4.nextPageable.afterCursor).isNotNull()
                assertThat(page4.hasNext).isFalse()

                // using before cursor to move back from third page, to get first two posts
                val beforePageable = PageRequest(
                    pageSize = 2,
                    beforeCursor = page3.nextPageable.beforeCursor,
                    sort = Sort.by(PUBLISHED_AT, direction = DESC),
                )
                val beforePage = underTest.execute(GetCreatorPosts("lemur", "pablo", beforePageable))

                assertThat(beforePage.content.map { it.post })
                    .containsExactly(post4, post3)
            }

            @Test
            fun `should page correctly and sort by published at asc, pinned at is ignored in this case`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val post1 = testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 3.days)

                val post3 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(PUBLISHED_AT, direction = ASC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post2)

                val page3 = underTest.execute(GetCreatorPosts("lemur", "pablo", page2.nextPageable))
                assertThat(page3.content.map { it.post })
                    .containsExactly(post3)

                val page4 = underTest.execute(GetCreatorPosts("lemur", "pablo", page3.nextPageable))
                assertThat(page4.content.map { it.post })
                    .containsExactly(post4)
                assertThat(page4.nextPageable.afterCursor).isNotNull()
                assertThat(page4.hasNext).isFalse()
            }
        }

        @Nested
        inner class GetCreatorPostSortingWatchedAt {
            @Test
            fun `should sort by watched_at desc, and then by published_at`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val post1 = testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 3.days)

                val post3 = testHelper.createPost("pablo", id = "3", pinnedAt = null, publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", id = "4", pinnedAt = null, publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                testHelper.createUser("lemur")
                testHelper.createWatchActivity(post2, "asset-id", "lemur", watchedAt = now - 1.days)
                testHelper.createWatchActivity(post4, "asset-id", "lemur", watchedAt = now - 2.days)

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(WATCHED_AT, direction = DESC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post2)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post4)

                val page3 = underTest.execute(GetCreatorPosts("lemur", "pablo", page2.nextPageable))
                assertThat(page3.content.map { it.post })
                    .containsExactly(post3)

                val page4 = underTest.execute(GetCreatorPosts("lemur", "pablo", page3.nextPageable))
                assertThat(page4.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page4.nextPageable.afterCursor).isNotNull()
                assertThat(page4.hasNext).isFalse()
            }

            @Test
            fun `should sort by watched_at asc, and then by published_at`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val post1 = testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 3.days)

                val post3 = testHelper.createPost("pablo", id = "3", pinnedAt = null, publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", id = "4", pinnedAt = null, publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                testHelper.createUser("lemur")
                testHelper.createWatchActivity(post2, "asset-id", "lemur", watchedAt = now - 1.days)
                testHelper.createWatchActivity(post4, "asset-id", "lemur", watchedAt = now - 2.days)

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(WATCHED_AT, direction = ASC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post4)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post2)

                val page3 = underTest.execute(GetCreatorPosts("lemur", "pablo", page2.nextPageable))
                assertThat(page3.content.map { it.post })
                    .containsExactly(post1)

                val page4 = underTest.execute(GetCreatorPosts("lemur", "pablo", page3.nextPageable))
                assertThat(page4.content.map { it.post })
                    .containsExactly(post3)
                assertThat(page4.nextPageable.afterCursor).isNotNull()
                assertThat(page4.hasNext).isFalse()
            }

            @Test
            fun `should behave as sorting by published_at desc if user didn't watch anything`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val post1 = testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 3.days)

                val post3 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(WATCHED_AT, direction = DESC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post4)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post3)

                val page3 = underTest.execute(GetCreatorPosts("lemur", "pablo", page2.nextPageable))
                assertThat(page3.content.map { it.post })
                    .containsExactly(post2)

                val page4 = underTest.execute(GetCreatorPosts("lemur", "pablo", page3.nextPageable))
                assertThat(page4.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page4.nextPageable.afterCursor).isNotNull()
                assertThat(page4.hasNext).isFalse()
            }

            @Test
            fun `should behave as sorting by published_at asc if user didn't watch anything`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val post1 = testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 3.days)

                val post3 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(WATCHED_AT, direction = ASC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post2)

                val page3 = underTest.execute(GetCreatorPosts("lemur", "pablo", page2.nextPageable))
                assertThat(page3.content.map { it.post })
                    .containsExactly(post3)

                val page4 = underTest.execute(GetCreatorPosts("lemur", "pablo", page3.nextPageable))
                assertThat(page4.content.map { it.post })
                    .containsExactly(post4)
                assertThat(page4.nextPageable.afterCursor).isNotNull()
                assertThat(page4.hasNext).isFalse()
            }
        }

        @Nested
        inner class GetCreatorPostSortingViews {
            @Test
            fun `should sort by views desc, and then by published_at`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val post1 = testHelper.createPost("pablo", views = 0, publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", views = 1100, publishedAt = now - 3.days)

                val post3 = testHelper.createPost("pablo", views = 0, publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", views = 700, publishedAt = now - 1.days)

                // should not show up since it's deleted
                val post5 = testHelper.createPost("pablo", state = DELETED)
                testHelper.createDailyPostViewsStats(post5, 13600, LocalDate.now())
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                testHelper.createDailyPostViewsStats(post2, 600, LocalDate.now())
                testHelper.createDailyPostViewsStats(post2, 500, LocalDate.now().minusDays(1))

                testHelper.createDailyPostViewsStats(post4, 400, LocalDate.now())
                testHelper.createDailyPostViewsStats(post4, 300, LocalDate.now().minusDays(1))

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(VIEWS, direction = DESC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post2)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post4)

                val page3 = underTest.execute(GetCreatorPosts("lemur", "pablo", page2.nextPageable))
                assertThat(page3.content.map { it.post })
                    .containsExactly(post3)

                val page4 = underTest.execute(GetCreatorPosts("lemur", "pablo", page3.nextPageable))
                assertThat(page4.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page4.nextPageable.afterCursor).isNotNull()
                assertThat(page4.hasNext).isFalse()
            }

            @Test
            fun `should sort by views asc, and then by published_at`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val post1 = testHelper.createPost("pablo", views = 0, publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", views = 1100, publishedAt = now - 3.days)

                val post3 = testHelper.createPost("pablo", views = 0, publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", views = 700, publishedAt = now - 1.days)

                // should not show up since it's deleted
                val post5 = testHelper.createPost("pablo", id = "5", state = DELETED)
                testHelper.createDailyPostViewsStats(post5, 13600, LocalDate.now())
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                testHelper.createDailyPostViewsStats(post2, 600, LocalDate.now())
                testHelper.createDailyPostViewsStats(post2, 500, LocalDate.now().minusDays(1))

                testHelper.createDailyPostViewsStats(post4, 400, LocalDate.now())
                testHelper.createDailyPostViewsStats(post4, 300, LocalDate.now().minusDays(1))

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(VIEWS, direction = ASC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post3)

                val page3 = underTest.execute(GetCreatorPosts("lemur", "pablo", page2.nextPageable))
                assertThat(page3.content.map { it.post })
                    .containsExactly(post4)

                val page4 = underTest.execute(GetCreatorPosts("lemur", "pablo", page3.nextPageable))
                assertThat(page4.content.map { it.post })
                    .containsExactly(post2)
                assertThat(page4.nextPageable.afterCursor).isNotNull()
                assertThat(page4.hasNext).isFalse()
            }

            @Test
            fun `should behave as sorting by published_at desc if posts do not have any views`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val post1 = testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 3.days)

                val post3 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(VIEWS, direction = DESC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post4)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post3)

                val page3 = underTest.execute(GetCreatorPosts("lemur", "pablo", page2.nextPageable))
                assertThat(page3.content.map { it.post })
                    .containsExactly(post2)

                val page4 = underTest.execute(GetCreatorPosts("lemur", "pablo", page3.nextPageable))
                assertThat(page4.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page4.nextPageable.afterCursor).isNotNull()
                assertThat(page4.hasNext).isFalse()
            }

            @Test
            fun `should behave as sorting by published_at asc if posts do not have any views`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val post1 = testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 3.days)

                val post3 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", pinnedAt = null, publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(VIEWS, direction = ASC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post2)

                val page3 = underTest.execute(GetCreatorPosts("lemur", "pablo", page2.nextPageable))
                assertThat(page3.content.map { it.post })
                    .containsExactly(post3)

                val page4 = underTest.execute(GetCreatorPosts("lemur", "pablo", page3.nextPageable))
                assertThat(page4.content.map { it.post })
                    .containsExactly(post4)
                assertThat(page4.nextPageable.afterCursor).isNotNull()
                assertThat(page4.hasNext).isFalse()
            }
        }

        @Nested
        inner class GetCreatorPostSortingTextSearch {
            @Test
            fun `should by text relevancy`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()
                val post1 = testHelper.createPost("pablo", text = "Donald Trump")
                val post2 = testHelper.createPost(
                    "pablo",
                    publishedAt = now - 1.days,
                    text = "milovnici dona Trumpa jsou koŠŤAta",
                )

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(QUERY_SIMILARITY, direction = DESC))
                val filter = GetCreatorPostsFilter(query = "DONALD TRUMP")
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1, filter))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable, filter))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post2)
                assertThat(page2.nextPageable.afterCursor).isNotNull()
                assertThat(page2.hasNext).isFalse()
            }
        }
    }

    @Nested
    inner class GetCreatorPostsFiltering {
        @Nested
        inner class GetCreatorPostsFilteringWatchedAt {
            @Test
            fun `should filter only posts in progress and sort them by published_at`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()

                // should be ignored since it's not watched
                testHelper.createPost("pablo", pinnedAt = now - 1.days, publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", pinnedAt = now - 2.days, publishedAt = now - 3.days)

                // should be ignored since it's not watched
                testHelper.createPost("pablo", id = "3", pinnedAt = null, publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", id = "4", pinnedAt = null, publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                testHelper.createUser("lemur")
                testHelper.createWatchActivity(post2, "asset-id", "lemur", watchedAt = now - 1.days)
                testHelper.createWatchActivity(post4, "asset-id", "lemur", watchedAt = now - 2.days)

                val filter = GetCreatorPostsFilter(type = PostFilterType.IN_PROGRESS)
                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(PUBLISHED_AT, direction = ASC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1, filter))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post2)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable, filter))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post4)

                assertThat(page2.nextPageable.afterCursor).isNotNull()
                assertThat(page2.hasNext).isFalse()
            }
        }

        @Nested
        inner class GetCreatorPostsFilteringCreatorId {
            @Test
            fun `should filter posts from given creator sorted by published at`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()

                val post1 = testHelper.createPost("pablo", publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", publishedAt = now - 3.days)
                val post3 = testHelper.createPost("pablo", publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(PUBLISHED_AT, direction = ASC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", "pablo", pageRequest1))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", "pablo", page1.nextPageable))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post2)

                val page3 = underTest.execute(GetCreatorPosts("lemur", "pablo", page2.nextPageable))
                assertThat(page3.content.map { it.post })
                    .containsExactly(post3)

                val page4 = underTest.execute(GetCreatorPosts("lemur", "pablo", page3.nextPageable))
                assertThat(page4.content.map { it.post })
                    .containsExactly(post4)
                assertThat(page4.nextPageable.afterCursor).isNotNull()
                assertThat(page4.hasNext).isFalse()
            }

            @Test
            fun `should return only posts from subscribed creators sorted by published at`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()

                val post1 = testHelper.createPost("pablo", publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", publishedAt = now - 3.days)

                testHelper.createPost("cestmir", publishedAt = now - 2.days)
                testHelper.createPost("cestmir", publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                testHelper.createUser("lemur")
                testHelper.createSubscription("lemur", "pablo", status = SubscriberStatus.ACTIVE)
                testHelper.createSubscription("lemur", "cestmir", status = SubscriberStatus.CANCELLED)

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(PUBLISHED_AT, direction = ASC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", null, pageRequest1))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", null, page1.nextPageable))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post2)
                assertThat(page2.nextPageable.afterCursor).isNotNull()
                assertThat(page2.hasNext).isFalse()
            }

            @Test
            fun `should filter only posts in progress from all subscriptions and sort them by published_at`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()

                val post1 = testHelper.createPost("cestmir", publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", publishedAt = now - 3.days)

                val post3 = testHelper.createPost("cestmir", publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                testHelper.createUser("lemur")
                testHelper.createWatchActivity(
                    post2,
                    "asset-id",
                    "lemur",
                    watchedAt = now - 1.days,
                    subscriptionActive = true,
                )
                testHelper.createWatchActivity(
                    post4,
                    "asset-id",
                    "lemur",
                    watchedAt = now - 2.days,
                    subscriptionActive = true,
                )

                // watched but subscription is no longer active
                testHelper.createWatchActivity(
                    post1,
                    "asset-id",
                    "lemur",
                    watchedAt = now - 1.days,
                    subscriptionActive = false,
                )
                testHelper.createWatchActivity(
                    post3,
                    "asset-id",
                    "lemur",
                    watchedAt = now - 2.days,
                    subscriptionActive = false,
                )

                val filter = GetCreatorPostsFilter(type = PostFilterType.IN_PROGRESS)
                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(PUBLISHED_AT, direction = ASC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", null, pageRequest1, filter))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post2)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", null, page1.nextPageable, filter))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post4)

                assertThat(page2.nextPageable.afterCursor).isNotNull()
                assertThat(page2.hasNext).isFalse()
            }

            @Test
            fun `should return all posts from subscribed creators that are in progress sorted by watched_at`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()

                val post1 = testHelper.createPost("cestmir", publishedAt = now - 5.days)
                val post2 = testHelper.createPost("pablo", publishedAt = now - 3.days)

                testHelper.createPost("cestmir", publishedAt = now - 2.days)
                val post4 = testHelper.createPost("pablo", publishedAt = now - 1.days)

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now)
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED)

                testHelper.createUser("lemur")
                testHelper.createWatchActivity(
                    post2,
                    "asset-id",
                    "lemur",
                    watchedAt = now - 1.days,
                    subscriptionActive = true,
                )
                testHelper.createWatchActivity(
                    post4,
                    "asset-id",
                    "lemur",
                    watchedAt = now - 2.days,
                    subscriptionActive = true,
                )
                testHelper.createWatchActivity(
                    post1,
                    "asset-id",
                    "lemur",
                    watchedAt = now - 3.days,
                    subscriptionActive = true,
                )

                val filter = GetCreatorPostsFilter(type = PostFilterType.IN_PROGRESS)
                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(WATCHED_AT, direction = DESC))
                val page1 = underTest.execute(GetCreatorPosts("lemur", null, pageRequest1, filter))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post2)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", null, page1.nextPageable, filter))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post4)

                val page3 = underTest.execute(GetCreatorPosts("lemur", null, page2.nextPageable, filter))
                assertThat(page3.content.map { it.post })
                    .containsExactly(post1)
                assertThat(page3.nextPageable.afterCursor).isNotNull()
                assertThat(page3.hasNext).isFalse()
            }
        }

        @Nested
        inner class GetCreatorPostsFilteringTextQuery {
            @Test
            fun `should return only posts that have relevant text and sort by published at desc`() {
                val underTest = CreatorPostQueryService(
                    TestCollections.subscribersCollection,
                    TestCollections.categoriesCollection,
                    TestCollections.savedPostsCollection,
                    TestRepositories.postRepository,
                    lazyTestContext,
                )

                val now = Instant.now()

                testHelper.createPost("pablo", publishedAt = now - 5.days, text = "irrelevant")
                testHelper.createPost("pablo", publishedAt = now - 3.days, text = "random")
                testHelper.createPost("pablo", publishedAt = now - 2.days, text = "Fialovo drahota")
                // user should be able to search for his own posts
                val post4 = testHelper.createPost(
                    "lemur",
                    publishedAt = now - 1.days,
                    text = "milovnici Trumpa jsou koŠŤAta",
                )
                val post5 = testHelper.createPost(
                    "pablo",
                    publishedAt = now - 2.days,
                    text = "kost jsou doležité",
                )

                // should not show up since it's deleted
                testHelper.createPost("pablo", state = DELETED, publishedAt = now, text = "kost")
                // should not show up since it's SCHEDULED
                testHelper.createPost("pablo", state = SCHEDULED, text = "kost")
                testHelper.createSubscription("lemur", "pablo", status = SubscriberStatus.ACTIVE)

                val pageRequest1 = PageRequest(pageSize = 1, sort = Sort.by(PUBLISHED_AT, direction = DESC))
                val filter = GetCreatorPostsFilter(query = "kost")
                val page1 = underTest.execute(GetCreatorPosts("lemur", null, pageRequest1, filter))
                assertThat(page1.content.map { it.post })
                    .containsExactly(post4)
                assertThat(page1.hasNext).isTrue()

                val page2 = underTest.execute(GetCreatorPosts("lemur", null, page1.nextPageable, filter))
                assertThat(page2.content.map { it.post })
                    .containsExactly(post5)
                assertThat(page2.hasNext).isFalse()
            }
        }
    }
}
