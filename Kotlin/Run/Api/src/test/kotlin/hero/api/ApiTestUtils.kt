package hero.api

import hero.baseutils.truncated
import hero.model.Chapter
import hero.model.GjirafaAsset
import hero.model.GjirafaLiveAsset
import hero.model.GjirafaStatus
import hero.model.LiveVideoStatus
import hero.model.Post
import hero.model.PostAsset
import hero.model.PostCounts
import hero.model.topics.PostState
import java.time.Instant
import java.util.UUID

fun gjirafaAsset(id: String) =
    GjirafaAsset(
        key = "encode-key",
        audioByteSize = 0,
        audioStaticUrl = "audio-url",
        audioStreamUrl = "audio-stream",
        chaptersVttUrl = "chapters-vtt-url",
        duration = 0.0,
        hasAudio = true,
        hasVideo = true,
        hidden = false,
        id = id,
        status = GjirafaStatus.ERROR,
        encodingFinishTime = null,
        encodingRemainingTime = null,
        createdAt = null,
        projectId = "project-id",
        imageKey = "1264567897",
    )

fun gjirafaLiveAsset(
    id: String = "live",
    status: LiveVideoStatus = LiveVideoStatus.LIVE,
) = PostAsset(gjirafaLive = GjirafaLiveAsset(id = id, "", "", status))

fun post(
    userId: String,
    parentUserId: String? = null,
    parentPostId: String? = null,
    text: String = "Post text",
    textHtml: String = text,
    textDelta: String = text,
    id: String = UUID.randomUUID().toString(),
    parentId: String? = null,
    siblingId: String? = null,
    state: PostState = PostState.PUBLISHED,
    messageThreadId: String? = null,
    assets: List<PostAsset> = listOf(),
    publishedAt: Instant = Instant.now(),
    pinnedAt: Instant? = null,
    price: Long? = null,
    categories: List<String> = listOf(),
    counts: PostCounts = PostCounts(),
    createdAt: Instant = Instant.now().truncated(),
    updatedAt: Instant = Instant.now().truncated(),
    notifiedAt: Instant = Instant.now().truncated(),
    chapters: List<Chapter> = listOf(),
    excludeFromRss: Boolean = false,
    isAgeRestricted: Boolean = false,
    isSponsored: Boolean = false,
) = Post(
    id = id,
    text = text,
    textHtml = textHtml,
    textDelta = textDelta,
    state = state,
    userId = userId,
    parentId = parentId,
    siblingId = siblingId,
    parentUserId = parentUserId,
    parentPostId = parentPostId,
    published = publishedAt.truncated(),
    pinnedAt = pinnedAt?.truncated(),
    messageThreadId = messageThreadId,
    assets = assets,
    assetIds = assets.mapNotNull { it.gjirafa?.id ?: it.gjirafaLive?.id },
    price = price,
    counts = counts,
    chapters = chapters,
    categories = categories,
    created = createdAt,
    updated = updatedAt,
    notifiedAt = notifiedAt,
    excludeFromRss = excludeFromRss,
    isAgeRestricted = isAgeRestricted,
    isSponsored = isSponsored,
)
