FROM eclipse-temurin:21-jdk

RUN curl -LO https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/download/v2.14.0/opentelemetry-javaagent.jar

# See https://linear.app/herohero/issue/HH-1399/implement-cloud-profiling
RUN mkdir -p /opt/cprof && \
    wget -q -O- https://storage.googleapis.com/cloud-profiler/java/latest/profiler_java_agent.tar.gz \
    | tar xzv -C /opt/cprof

COPY ./build/shadow/app.jar /app.jar

# we cannot set build-time arg TARGET_NAME in CMD below
ENV PROFILER_ARG="-agentpath:/opt/cprof/profiler_java_agent.so=-cprof_service=${TARGET_NAME},-cprof_enable_heap_sampling=true,-logtostderr,-minloglevel=2"

# allowRestrictedHeaders needed for Image service
CMD java $PROFILER_ARG -javaagent:opentelemetry-javaagent.jar -XX:MaxRAMPercentage=75 -Dsun.net.http.allowRestrictedHeaders=true -jar /app.jar
