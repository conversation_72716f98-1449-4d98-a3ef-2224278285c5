.Kotlin/Run/Auth/variables:
  variables:
    SERVICE_NAME: auth
    SENTRY_DSN: https://<EMAIL>/4506044757377024
    OTEL_SAMPLE_RATE: 0
    ENV_VARS: "FACEBOOK_SECRET=$FACEBOOK_SECRET,SQL_MAX_POOL_SIZE=2"

Kotlin/Run/Auth/build:
  stage: build-services
  needs:
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/Exceptions/build
    - Kotlin/Modules/Firebase/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Http4k/build
    - Kotlin/Modules/Jwt/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Contract/build
    - Kotlin/Modules/Spotify/build
    - Kotlin/Modules/Testing/build
    - Kotlin/Modules/Repository/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/Run/Auth/variables
    - .Kotlin/job-build-gradle-service

Kotlin/Run/Auth/deploy-devel:
  stage: deploy-devel
  extends:
    - .Kotlin/Run/deploy-devel
    - .Kotlin/Run/Auth/variables
  needs:
    - Kotlin/Run/Auth/build
  variables:
    MEMORY: 1024Mi

Kotlin/Run/Auth/deploy-staging:
  stage: deploy-staging
  extends:
    - .Kotlin/Run/deploy-staging
    - .Kotlin/Run/Auth/variables
  needs:
    - Kotlin/Run/Auth/build
  variables:
    MEMORY: 1024Mi

Kotlin/Run/Auth/deploy-prod:
  stage: deploy-prod
  variables:
    MAX_INSTANCES: 2
    THROTTLING: "no"
    ENV_VARS: "FACEBOOK_SECRET=$FACEBOOK_SECRET,SQL_MAX_POOL_SIZE=4"
  extends:
    - .Kotlin/Run/deploy-prod
    - .Kotlin/Run/Auth/variables
  needs:
    - Kotlin/Run/Auth/build
