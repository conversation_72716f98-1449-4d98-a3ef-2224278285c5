Kotlin/Function/Rss/SpotifyAccessGranter/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Jwt/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Spotify/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Rss/SpotifyAccessGranter/variables:
  variables:
    FUNCTION_NAME: "spotify-access-granter"
    CLASS_NAME: "hero.functions.SpotifyAccessGranter"
    TOPIC: "SubscriberStatusChanged"
    SERVICE_ACCOUNT: "<EMAIL>"
    ADDITIONAL_PARAMETERS: "--retry"

Kotlin/Function/Rss/SpotifyAccessGranter/deploy-devel:
  needs:
    - Kotlin/Function/Rss/SpotifyAccessGranter/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Rss/SpotifyAccessGranter/variables

Kotlin/Function/Rss/SpotifyAccessGranter/deploy-staging:
  needs:
    - Kotlin/Function/Rss/SpotifyAccessGranter/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/Rss/SpotifyAccessGranter/variables

Kotlin/Function/Rss/SpotifyAccessGranter/deploy-prod:
  needs:
    - Kotlin/Function/Rss/SpotifyAccessGranter/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Rss/SpotifyAccessGranter/variables
