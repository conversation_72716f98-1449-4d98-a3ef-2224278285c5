# Stripe Interchange fees writer

Since our pricing model was migrated to Interchange++, the fees are not showing correctly for individual transactions
and these show `zero` amount instead. 

Stripe receives the Interchange prices for each transaction with some delay and one can generate CSV reports
of those fees either manually or via API.

This consumer processes these reports which were generated based on Stripe webhooks and writes those fees 
in metadata of each affected transaction. E.g. see here:

https://dashboard.stripe.com/payments/pi_3RLNgkB6ZCHekl2R0RihnO99
