Kotlin/Function/Stripe/StripeUnfinishedAccountsNotifier/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Stripe/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Stripe/StripeUnfinishedAccountsNotifier/variables:
  variables:
    FUNCTION_NAME: "stripe-unfinished-accounts-notifier"
    CLASS_NAME: "hero.functions.StripeUnfinishedAccountsNotifier"
    TOPIC: "Daily"
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/Stripe/StripeUnfinishedAccountsNotifier/deploy-devel:
  needs:
    - Kotlin/Function/Stripe/StripeUnfinishedAccountsNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Stripe/StripeUnfinishedAccountsNotifier/variables

Kotlin/Function/Stripe/StripeUnfinishedAccountsNotifier/deploy-prod:
  needs:
    - Kotlin/Function/Stripe/StripeUnfinishedAccountsNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Stripe/StripeUnfinishedAccountsNotifier/variables
  variables:
    TIMEOUT: "300s"
