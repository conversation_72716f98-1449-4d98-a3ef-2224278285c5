# Draft Invoices retrier

When connected account becomes deactivated, all its subscription payments are created in "Draft" state
and are never re-tried charging. This can be seen for instance here:

https://dashboard.stripe.com/invoices/in_1Pv1QdB6ZCHekl2RCzMyPA4h

```json
{
  "last_finalization_error": {
    "message": "The account referenced in the 'destination' parameter is missing the required capabilities: transfers or legacy_payments are required on the 'destination' account.",
    "type": "invalid_request_error"
  }
}
```

This function runs daily and tries to search for accounts which has recently become active again
and tries to re-enable auto-collection of affected invoices and subsequently perform charges.
