# Stripe Apple Transfers Handling

When user makes a purchase via iOS app, the charge is stored in Apple and paid out directly
to Herohero account. To make sure the creator receives their funds, we need to mirror these
transactions in Stripe and perform transfers to connected accounts.

Example:

1. User makes a payment in Apple
2. Apple sends a webhook to our API with `DID_RENEW` notification type
3. Webhook handler stores a AppleTransfer object in Firestore to signify we need to perform a transfer
4. This function is triggered every hour and makes corresponding transfers in Stripe
5. We DO NOT transfer the Apple funds to Stripe. We only use our current Stripe balance to fulfill the transfer.
6. If there are not enough funds in Stripe, the transfer fails and is retried next hour.
