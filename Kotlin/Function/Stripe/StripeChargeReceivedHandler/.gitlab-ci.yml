Kotlin/Function/Stripe/StripeChargeReceivedHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/Stripe/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/Repository/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Stripe/StripeChargeReceivedHandler/variables:
  variables:
    FUNCTION_NAME: "stripe-charge-received-handler"
    CLASS_NAME: "hero.functions.StripeChargeReceivedHandler"
    TOPIC: "StripeChargeReceived"
    # TODO limit service account

Kotlin/Function/Stripe/StripeChargeReceivedHandler/deploy-devel:
  needs:
    - Kotlin/Function/Stripe/StripeChargeReceivedHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Stripe/StripeChargeReceivedHandler/variables

Kotlin/Function/Stripe/StripeChargeReceivedHandler/deploy-prod:
  needs:
    - Kotlin/Function/Stripe/StripeChargeReceivedHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Stripe/StripeChargeReceivedHandler/variables
  variables:
    TIMEOUT: "300s"
