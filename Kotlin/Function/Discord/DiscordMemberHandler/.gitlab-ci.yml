Kotlin/Function/Discord/DiscordMemberHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Discord/DiscordMemberHandler/variables:
  variables:
    FUNCTION_NAME: "discord-member-handler"
    CLASS_NAME: "hero.functions.DiscordMemberHandler"
    TOPIC: "DiscordMemberChanged"
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/Discord/DiscordMemberHandler/deploy-devel:
  needs:
    - Kotlin/Function/Discord/DiscordMemberHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Discord/DiscordMemberHandler/variables

Kotlin/Function/Discord/DiscordMemberHandler/deploy-staging:
  needs:
    - Kotlin/Function/Discord/DiscordMemberHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/Discord/DiscordMemberHandler/variables

Kotlin/Function/Discord/DiscordMemberHandler/deploy-prod:
  needs:
    - Kotlin/Function/Discord/DiscordMemberHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Discord/DiscordMemberHandler/variables
