Kotlin/Function/Discord/DiscordSubscriptionHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Discord/DiscordSubscriptionHandler/variables:
  variables:
    FUNCTION_NAME: "discord-subscription-handler"
    CLASS_NAME: "hero.functions.DiscordSubscriptionHandler"
    TOPIC: "SubscriberStatusChanged"
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/Discord/DiscordSubscriptionHandler/deploy-devel:
  needs:
    - Kotlin/Function/Discord/DiscordSubscriptionHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Discord/DiscordSubscriptionHandler/variables

Kotlin/Function/Discord/DiscordSubscriptionHandler/deploy-staging:
  needs:
    - Kotlin/Function/Discord/DiscordSubscriptionHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/Discord/DiscordSubscriptionHandler/variables

Kotlin/Function/Discord/DiscordSubscriptionHandler/deploy-prod:
  needs:
    - Kotlin/Function/Discord/DiscordSubscriptionHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Discord/DiscordSubscriptionHandler/variables
