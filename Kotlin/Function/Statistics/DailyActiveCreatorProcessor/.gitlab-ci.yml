Kotlin/Function/Statistics/DailyActiveCreatorProcessor/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Statistics/DailyActiveCreatorProcessor/variables:
  variables:
    FUNCTION_NAME: "daily-active-creator-processor"
    CLASS_NAME: "hero.functions.DailyActiveCreatorProcessor"
    TOPIC: "Daily"
    # TODO limit service account to SQL

Kotlin/Function/Statistics/DailyActiveCreatorProcessor/deploy-devel:
  needs:
    - Kotlin/Function/Statistics/DailyActiveCreatorProcessor/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Statistics/DailyActiveCreatorProcessor/variables

Kotlin/Function/Statistics/DailyActiveCreatorProcessor/deploy-prod:
  needs:
    - Kotlin/Function/Statistics/DailyActiveCreatorProcessor/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Statistics/DailyActiveCreatorProcessor/variables
  variables:
    TIMEOUT: "540s"
    MAX_INSTANCES: 2
