package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.logging.Logger
import hero.model.topics.SubscriberStatusChange
import hero.model.topics.SubscriberStatusChanged
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables.WATCH_ACTIVITY
import org.jooq.DSLContext

@Suppress("Unused")
class SubscriptionWatchActivityHandler(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    systemEnv: EnvironmentVariables = SystemEnv,
    private val logger: Logger = log,
) : PubSubSubscriber<SubscriberStatusChanged>(systemEnv) {
    private val context: DSLContext by lazyContext

    override fun consume(payload: SubscriberStatusChanged) {
        val isSubscriptionActive = payload.statusChange == SubscriberStatusChange.SUBSCRIBED
        val userId = payload.userId
        val creatorId = payload.creatorId
        logger.info(
            "Changing watch activity active flag to $isSubscriptionActive " +
                "for a user $userId and creator $creatorId",
            mapOf("creatorId" to creatorId, "userId" to userId),
        )

        val f = context
            .update(WATCH_ACTIVITY)
            .set(WATCH_ACTIVITY.SUBSCRIPTION_ACTIVE, isSubscriptionActive)
            .where(WATCH_ACTIVITY.USER_ID.eq(userId).and(WATCH_ACTIVITY.CREATOR_ID.eq(creatorId)))
            .execute()
        println(f)
    }
}
