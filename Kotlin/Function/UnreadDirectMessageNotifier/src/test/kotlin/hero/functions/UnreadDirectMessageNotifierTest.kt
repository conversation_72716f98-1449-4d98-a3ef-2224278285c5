package hero.functions

import hero.baseutils.SystemEnv
import hero.gcloud.PubSub
import hero.model.Creator
import hero.model.MessageThread
import hero.model.User
import hero.model.topics.EmailPublished
import io.mockk.mockk
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

internal class UnreadDirectMessageNotifierTest {
    private val messageNotifier = UnreadDirectMessageNotifier(
        domain = "https://herohero.co",
        environment = "devel",
        pubSub = mockk(),
        firestore = mockk(),
        usersCollection = mockk(),
        threadsCollection = mockk(),
    )

    private val users = mapOf(
        "a" to User(id = "A", name = "A", path = "A", creator = Creator(tierId = "EUR05")),
        "b" to User(id = "B", name = "B", path = "B", creator = Creator(tierId = "EUR05")),
        "c" to User(id = "C", name = "C", path = "C", creator = Creator(tierId = "EUR05")),
    )

    @Test
    fun unseensSeparators() {
        // a
        assertFalse(messageNotifier.withComma(listOf("a").size, 0))
        assertFalse(messageNotifier.withAnd(listOf("a").size, 0))

        // a & b
        assertFalse(messageNotifier.withComma(listOf("a", "b").size, 0))
        assertFalse(messageNotifier.withComma(listOf("a", "b").size, 1))
        assertTrue(messageNotifier.withAnd(listOf("a", "b").size, 0))
        assertFalse(messageNotifier.withAnd(listOf("a", "b").size, 1))

        // a, b & c
        assertTrue(messageNotifier.withComma(listOf("a", "b", "c").size, 0))
        assertFalse(messageNotifier.withComma(listOf("a", "b", "c").size, 1))
        assertFalse(messageNotifier.withComma(listOf("a", "b", "c").size, 2))

        assertFalse(messageNotifier.withAnd(listOf("a", "b", "c").size, 0))
        assertTrue(messageNotifier.withAnd(listOf("a", "b", "c").size, 1))
        assertFalse(messageNotifier.withAnd(listOf("a", "b", "c").size, 2))
    }

    @Test
    fun mapMessagedUsers() {
        val messageThread1 = MessageThread(lastMessageBy = "a")
        val messageThread2 = MessageThread(lastMessageBy = "b")
        val messageThread3 = MessageThread(lastMessageBy = "c")
        assertEquals(
            listOf(
                mapOf(
                    "targetLink" to "https://herohero.co/target-user-id/messages/${messageThread1.id}",
                    "targetName" to "A",
                    "withComma" to false,
                    "withAnd" to false,
                ),
            ),
            messageNotifier.mapMessagedUsers(
                "target-user-id",
                listOf(messageThread1),
                users,
            ),
        )

        assertEquals(
            listOf(
                mapOf(
                    "targetLink" to "https://herohero.co/target-user-id/messages/${messageThread1.id}",
                    "targetName" to "A",
                    "withComma" to false,
                    "withAnd" to true,
                ),
                mapOf(
                    "targetLink" to "https://herohero.co/target-user-id/messages/${messageThread2.id}",
                    "targetName" to "B",
                    "withComma" to false,
                    "withAnd" to false,
                ),
            ),
            messageNotifier.mapMessagedUsers(
                "target-user-id",
                listOf(messageThread1, messageThread2),
                users,
            ),
        )

        assertEquals(
            listOf(
                mapOf(
                    "targetLink" to "https://herohero.co/target-user-id/messages/${messageThread1.id}",
                    "targetName" to "A",
                    "withComma" to true,
                    "withAnd" to false,
                ),
                mapOf(
                    "targetLink" to "https://herohero.co/target-user-id/messages/${messageThread2.id}",
                    "targetName" to "B",
                    "withComma" to false,
                    "withAnd" to true,
                ),
                mapOf(
                    "targetLink" to "https://herohero.co/target-user-id/messages/${messageThread3.id}",
                    "targetName" to "C",
                    "withComma" to false,
                    "withAnd" to false,
                ),
            ),
            messageNotifier.mapMessagedUsers(
                "target-user-id",
                listOf(messageThread1, messageThread2, messageThread3),
                users,
            ),
        )
    }

    @Test
    fun sendTestMessage() {
        val pubSub = PubSub("devel", SystemEnv.cloudProject)

        pubSub.publish(
            EmailPublished(
                to = "<EMAIL>",
                template = "new-direct-message",
                variables = listOf(
                    "messages" to messageNotifier.mapMessagedUsers(
                        "target-user-id",
                        listOf(
                            MessageThread(lastMessageBy = "a"),
                            MessageThread(lastMessageBy = "b"),
                            MessageThread(lastMessageBy = "c"),
                        ),
                        users,
                    ),
                    "user-id" to "admin",
                    "target-user-name" to "Author",
                    "user-name" to "Admin",
                ),
                language = "cs",
            ),
        )
    }
}
