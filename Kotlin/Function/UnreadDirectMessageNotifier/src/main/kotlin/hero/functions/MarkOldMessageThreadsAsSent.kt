package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.minusDays
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.isFalse
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.MessageThread
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.runBlocking
import java.time.Instant

fun main() {
    val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, true)
    val threadsCollection: TypedCollectionReference<MessageThread> = firestore.typedCollectionOf(MessageThread)

    val threads = threadsCollection
        .where(MessageThread::emailNotified).isFalse()
        .fetchAll()

    threads
        .count { messageThread ->
            messageThread.lastMessageAt?.isBefore(Instant.now().minusDays(5)) == true
        }
        .also { println("Older than 5 days: $it") }

    threads
        .count { messageThread ->
            messageThread.lastMessageAt?.isAfter(Instant.now().minusDays(5)) == true
        }
        .also { println("Newer than 5 days: $it") }

    runBlocking(Dispatchers.Default) {
        threads
            .filter {
                it.lastMessageAt?.isBefore(Instant.now().minusDays(5)) == true
            }
            .map {
                async {
                    println(it.id)
                    threadsCollection[it.id].field(MessageThread::emailNotified).update(true)
                }
            }
            .joinAll()
    }
}
