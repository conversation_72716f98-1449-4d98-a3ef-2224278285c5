Kotlin/Function/Accounting/InvoicesFlexibeeExporter/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/Stripe/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Accounting/InvoicesFlexibeeExporter/variables:
  variables:
    FUNCTION_NAME: "invoices-flexibee-exporter"
    CLASS_NAME: "hero.functions.InvoicesFlexibeeExporter"
    TRIGGER: "$FIRESTORE_TRIGGER=prod-invoices/{invoiceId}"
    TIMEOUT: "60s"
    SERVICE_ACCOUNT: "<EMAIL>"

# We can run this function only in production as we have only production Flexibee instance.
Kotlin/Function/Accounting/InvoicesFlexibeeExporter/deploy-prod:
  needs:
    - Kotlin/Function/Accounting/InvoicesFlexibeeExporter/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Accounting/InvoicesFlexibeeExporter/variables
