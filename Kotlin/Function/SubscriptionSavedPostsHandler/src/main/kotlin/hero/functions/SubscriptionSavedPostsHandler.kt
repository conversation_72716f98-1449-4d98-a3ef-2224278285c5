package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.SavedPost
import hero.model.topics.SubscriberStatusChange
import hero.model.topics.SubscriberStatusChanged

@Suppress("Unused")
class SubscriptionSavedPostsHandler(
    private val production: Boolean = SystemEnv.isProduction,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
    private val savedPostsCollection: TypedCollectionReference<SavedPost> = firestore.typedCollectionOf(SavedPost),
) : PubSubSubscriber<SubscriberStatusChanged>() {
    override fun consume(payload: SubscriberStatusChanged) {
        val isSubscriptionActive = payload.statusChange == SubscriberStatusChange.SUBSCRIBED
        val userId = payload.userId
        val creatorId = payload.creatorId
        log.info(
            "Changing saved posts to sub active flag to $isSubscriptionActive " +
                "for a user $userId and creator $creatorId",
            mapOf("creatorId" to creatorId, "userId" to userId),
        )

        savedPostsCollection
            .where(SavedPost::userId).isEqualTo(userId)
            .and(SavedPost::creatorId).isEqualTo(creatorId)
            .fetchAll()
            .forEach {
                savedPostsCollection[it.id].field(SavedPost::subscriptionActive).update(isSubscriptionActive)
            }
    }
}
