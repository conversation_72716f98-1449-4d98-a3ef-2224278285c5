Kotlin/Function/SubscriptionSavedPostsHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Jwt/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/SubscriptionSavedPostsHandler/variables:
  variables:
    FUNCTION_NAME: "subscription-saved-posts-handler"
    CLASS_NAME: "hero.functions.SubscriptionSavedPostsHandler"
    TOPIC: "SubscriberStatusChanged"
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/SubscriptionSavedPostsHandler/deploy-devel:
  needs:
    - Kotlin/Function/SubscriptionSavedPostsHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/SubscriptionSavedPostsHandler/variables

Kotlin/Function/SubscriptionSavedPostsHandler/deploy-prod:
  needs:
    - Kotlin/Function/SubscriptionSavedPostsHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/SubscriptionSavedPostsHandler/variables
