package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.logging.Logger
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.SavedPost
import hero.repository.post.SavedPostRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.time.Instant

@Suppress("Unused")
class SavedPostPostgresExport(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    systemEnv: EnvironmentVariables = SystemEnv,
    firestore: FirestoreRef = firestore(systemEnv.cloudProject, systemEnv.isProduction),
    private val savedPostsCollection: TypedCollectionReference<SavedPost> = firestore.typedCollectionOf(SavedPost),
    private val logger: Logger = log,
) : FirestoreEventSubcriber(environmentVariables = systemEnv, retryable = true) {
    private val context by lazyContext
    private val savedPostRepository: SavedPostRepository = SavedPostRepository(lazyContext)

    override fun consume(event: FirestoreEvent) {
        if (event.wasDeleted) {
            context
                .update(Tables.SAVED_POST)
                .set(Tables.SAVED_POST.DELETED_AT, Instant.now())
                .where(Tables.SAVED_POST.ID.eq(event.documentId))
                .execute()
        } else {
            val savedPost = savedPostsCollection[event.documentId].get()

            logger.info("Exporting saved post ${savedPost.id} to Postgres")
            savedPostRepository.save(savedPost)
            logger.info("Done exporting saved post ${savedPost.id} to Postgres")
        }
    }
}
