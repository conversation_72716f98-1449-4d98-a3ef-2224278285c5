Kotlin/Function/Post/PostSlackAlerter/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Post/PostSlackAlerter/variables:
  variables:
    FUNCTION_NAME: "post-slack-alerter"
    CLASS_NAME: "hero.functions.PostSlackAlerter"
    TOPIC: "PostStateChanged"
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/Post/PostSlackAlerter/deploy-devel:
  needs:
    - Kotlin/Function/Post/PostSlackAlerter/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Post/PostSlackAlerter/variables

Kotlin/Function/Post/PostSlackAlerter/deploy-prod:
  needs:
    - Kotlin/Function/Post/PostSlackAlerter/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Post/PostSlackAlerter/variables
