plugins {
    id("hero.cloud-function-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    implementation(projectModule(":Modules:Jwt"))
    implementation(projectModule(":Modules:Firebase"))
    implementation(projectModule(":Modules:SQL"))
    implementation(projectModule(":Modules:Repository"))
    implementation(projectModule(":Function:Subscriber"))

    testImplementation(projectModule(":Modules:IntegrationTesting"))
}
