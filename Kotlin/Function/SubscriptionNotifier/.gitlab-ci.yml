Kotlin/Function/SubscriptionNotifier/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/Repository/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/SubscriptionNotifier/variables:
  variables:
    FUNCTION_NAME: "subscription-notifier"
    CLASS_NAME: "hero.functions.SubscriptionNotifier"
    TOPIC: "SubscriberStatusChanged"
    # TODO limit service account to SQL

Kotlin/Function/SubscriptionNotifier/deploy-devel:
  needs:
    - Kotlin/Function/SubscriptionNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/SubscriptionNotifier/variables

Kotlin/Function/SubscriptionNotifier/deploy-staging:
  needs:
    - Kotlin/Function/SubscriptionNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/SubscriptionNotifier/variables

Kotlin/Function/SubscriptionNotifier/deploy-prod:
  needs:
    - Kotlin/Function/SubscriptionNotifier/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/SubscriptionNotifier/variables
