<component name="ProjectRunConfigurationManager">
  <configuration default="true" type="JUnit" factoryName="JUnit">
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="true" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        <ENTRY IS_ENABLED="true" PARSER="env" IS_EXECUTABLE="false" PATH=".env" />
      </ENTRIES>
    </extension>
    <option name="MAIN_CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="TEST_OBJECT" value="class" />
    <option name="VM_PARAMETERS" value="-ea --add-opens=java.base/java.time=ALL-UNNAMED" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>