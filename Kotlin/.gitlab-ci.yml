include:
  - "/Kotlin/Modules/.gitlab-ci.yml"
  - "/Kotlin/Function/.gitlab-ci.yml"
  - "/Kotlin/Run/.gitlab-ci.yml"

.Kotlin/base-variables:
  variables:
    IMAGE_BASE: europe-west1-docker.pkg.dev/${CLOUD_PROJECT}/cloud-run

# Note that all tasks must start with corresponding directory to be properly cacheable.
# See GitLab/*.py which uses these to extract directory structure of dependencies.
.Kotlin/job-build-gradle-module:
  extends:
    - .run-always
    - .variables-build
  variables:
    TEST_ARTIFACTS_PATTERN: "**/test-results/test/TEST-*.xml"
    DETEKT_REPORT: "**/reports/detekt/detekt.html"
    # this will never be used, just needed for build
    SERVICE_TYPE: cloud_function
  artifacts:
    paths:
      - "**/reports/detekt/detekt.html"
    reports:
      junit: "**/test-results/test/TEST-*.xml"
      codequality: "**/reports/detekt/gitlab.json"
  script:
    - MODULE_HASH=$($CI_PROJECT_DIR/GitLab/module-hash.py $CI_JOB_NAME)
    - GCS_CACHE_URL=gs://heroheroco-gitlab-build-cache/v1/$MODULE_HASH.tar
    - GCS_ARTIFACTS_URL=gs://heroheroco-gitlab-build-cache/artifacts-v1/$MODULE_HASH.tar
    # Skip build if cache already exists. Also restore artifacts (test reports, ...) to help GitLab MR reports.
    - if gsutil -q stat $GCS_CACHE_URL >/dev/null; then
        echo "Cached module artifacts $GCS_CACHE_URL exist, skipping its build.";
        echo "To circumvent caching delete the cache entry with \'gsutil rm $GCS_CACHE_URL\'";
        gsutil cp $GCS_ARTIFACTS_URL - | tar -xv;
        exit;
      else
        echo "Building module with hash $MODULE_HASH.";
      fi
    # Check-out artifacts of dependencies
    - $CI_PROJECT_DIR/GitLab/restore-dependencies.py $CI_JOB_NAME
    - gradle -v
    # Build and install into local Maven repository, goes to .m2 due to symlinks
    # We shouldn't stop daemon here, as it will be reused by other jobs (--no-daemon).
    - gradle check publish -Pci=true --warning-mode all
    # Save artifacts (test reports, ...) for reuse by cached jobs
    - tar -cv --ignore-failed-read $TEST_ARTIFACTS_PATTERN $DETEKT_REPORT | gsutil cp - $GCS_ARTIFACTS_URL
    # Upload built artifacts to module cache on GCS. Use tar so that this is atomic (everything in one blob)
    - cd $CI_PROJECT_DIR/m2
    - tar -cv * | gsutil cp - $GCS_CACHE_URL

.Kotlin/job-build-gradle-service:
  extends:
    - .run-always
    - .variables-build
    - .Kotlin/base-variables
  variables:
    TEST_ARTIFACTS_PATTERN: "**/test-results/test/TEST-*.xml"
    DETEKT_REPORT: "**/reports/detekt/detekt.html"
    # this will never be used, just needed for build
    SERVICE_TYPE: cloud_function
  script:
    - if [ -z "${SERVICE_NAME}" ]; then echo SERVICE_NAME must be set, failing.; exit 1; fi
    - MODULE_HASH=$($CI_PROJECT_DIR/GitLab/module-hash.py $CI_JOB_NAME Kotlin/Run/.gitlab-ci.yml)
    - export IMAGE_TAG=$IMAGE_BASE/$SERVICE_NAME:$MODULE_HASH
    - GCS_ARTIFACTS_URL=gs://heroheroco-gitlab-build-cache/artifacts-v1/$MODULE_HASH.tar
    # Skip build if cache already exists. Also restore artifacts (test reports, ...) to help GitLab MR reports.
    - if gsutil -q stat $GCS_ARTIFACTS_URL && docker manifest inspect $IMAGE_TAG >/dev/null; then
        echo "Image $IMAGE_TAG already in remote repository, skipping its build.";
        echo "To force building the image, run \`gcloud container images delete $IMAGE_TAG\`.";
        gsutil cp $GCS_ARTIFACTS_URL - | tar -xv;
        exit;
      else
        echo "Building with image $IMAGE_TAG.";
      fi
    # Check-out artifacts of dependencies
    - gradle -v
    - $CI_PROJECT_DIR/GitLab/restore-dependencies.py $CI_JOB_NAME
    # We shouldn't stop daemon here, as it will be reused by other jobs (--no-daemon).
    - gradle build -Pci=true --warning-mode all
    - docker build -t $IMAGE_TAG --build-arg TARGET_NAME=$ENVIRONMENT-$SERVICE_NAME -f ../Dockerfile .
    - docker push $IMAGE_TAG
    - docker manifest inspect $IMAGE_TAG > /dev/null  # safeguard: this fails if computed image tag does not exist
    # Save artifacts (test reports, ...) for reuse by cached jobs
    - tar -cv --ignore-failed-read $TEST_ARTIFACTS_PATTERN $DETEKT_REPORT | gsutil cp - $GCS_ARTIFACTS_URL
  artifacts:
    paths:
      - "**/reports/detekt/detekt.html"
    reports:
      junit: "**/test-results/test/TEST-*.xml"
      codequality: "**/reports/detekt/gitlab.json"

