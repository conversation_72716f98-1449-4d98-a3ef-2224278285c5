package hero.gcloud

import com.google.api.core.ApiFuture
import com.google.cloud.firestore.CollectionReference
import com.google.cloud.firestore.DocumentReference
import com.google.cloud.firestore.DocumentSnapshot
import com.google.cloud.firestore.FieldValue
import hero.exceptions.http.NotFoundException
import kotlin.reflect.KClass
import kotlin.reflect.KProperty1

open class TypedCollectionReference<T : Any>(
    internal val collectionReference: CollectionReference,
    internal val type: KClass<T>,
) {
    open operator fun get(id: String): Document<T> = Document(collectionReference.document(id), type)

    operator fun contains(id: String) = this[id].fetch() != null

    companion object {
        inline operator fun <reified T : Any> invoke(collectionReference: CollectionReference) =
            TypedCollectionReference(collectionReference, T::class)
    }
}

open class Document<R : Any> internal constructor(internal val doc: DocumentReference, internal val type: KClass<R>) {
    open fun <T, V> field(path: DocumentPathBuilder<R, T, V>) = DocumentField(doc, path.build())

    open fun <T> field(property: KProperty1<R, T>) =
        DocumentField(doc, DocumentPath<R, T>(DocumentRoot(type), property.name))

    fun delete() {
        doc.delete().get()
    }

    open fun fetch() =
        retryOnDeadline {
            fetchSource()
                .get()
                .data
                ?.let {
                    // see here
                    mapper.convertValue(it, type.java)
                }
        }

    protected open fun fetchSource(): ApiFuture<DocumentSnapshot> = doc.get()

    fun get() = fetch() ?: throw NotFoundException("Entity ${type.simpleName} with id ${doc.id} not found")

    open fun set(value: R) {
        retryOnDeadline {
            doc.set(value).get()
        }
    }
}

open class DocumentField<R : Any, V> internal constructor(
    internal val doc: DocumentReference,
    internal val path: DocumentPath<R, V>,
    internal val valueTransformer: (V) -> Any? = { it },
) {
    open fun update(value: V) {
        retryOnDeadline { doc.update(path.path, valueTransformer(value)).get() }
    }
}

fun <R : Any, V : Number?> DocumentField<R, V>.increment(by: Long) {
    retryOnDeadline {
        doc.update(path.path, FieldValue.increment(by)).get()
    }
}

/**
 * The parameter aHack is to solve the issue with platform declaration clash, where both function would have the same
 * signature on jvm. Adding the default parameter actually solves this, is pretty hacky though.
 */
fun <R : Any, V> DocumentField<R, List<V>?>.remove(
    value: V,
    aHack: Boolean = true,
) {
    doc.update(path.path, FieldValue.arrayRemove(value)).get()
}

fun <R : Any, V> DocumentField<R, List<V>>.remove(value: V) {
    doc.update(path.path, FieldValue.arrayRemove(value)).get()
}

/**
 * The parameter aHack is to solve the issue with platform declaration clash, where both function would have the same
 * signature on jvm. Adding the default parameter actually solves this, is pretty hacky though.
 */
fun <R : Any, V> DocumentField<R, List<V>?>.union(
    value: V,
    aHack: Boolean = true,
) {
    doc.update(path.path, FieldValue.arrayUnion(value)).get()
}

fun <R : Any, V> DocumentField<R, List<V>>.union(value: V) {
    doc.update(path.path, FieldValue.arrayUnion(value)).get()
}
