package hero.gcloud

import com.google.api.client.json.gson.GsonFactory
import com.google.api.services.drive.Drive
import com.google.api.services.drive.DriveScopes
import com.google.api.services.drive.model.File
import com.google.auth.http.HttpCredentialsAdapter
import com.google.auth.oauth2.GoogleCredentials
import java.io.ByteArrayOutputStream
import java.util.Collections

object SharedDriveIds {
    const val CREATOR_SUBSCRIBERS_REPORTS = "0ANKDc1s1eJMeUk9PVA"
    const val INVOICES = "0ABBhl7IlmlkOUk9PVA"
    const val INVOICE_REPORTS = "0AGEC5H_E3hLUUk9PVA"
    const val DATA_ROOM = "0AHAabMgXXC11Uk9PVA"
}

internal val driveService: Drive = GoogleCredentials
    .getApplicationDefault()
    .createScoped(Collections.singleton(DriveScopes.DRIVE))
    .let {
        HttpCredentialsAdapter(it)
    }
    .let {
        Drive.Builder(
            httpTransport,
            GsonFactory.getDefaultInstance(),
            it,
        ).setApplicationName("Herohero drive").build()
    }

internal const val FOLDER_MIME_TYPE = "application/vnd.google-apps.folder"
internal const val SPREADSHEET_MIME_TYPE = "application/vnd.google-apps.spreadsheet"
internal const val EXCEL_MIME_TYPE = "application/vnd.ms-excel"

fun listDrives(): List<SharedDrive> =
    driveService.drives().list().execute()
        .asSequence()
        .filter { it.key == "drives" }
        .mapNotNull { it.value as? List<*> }
        .flatMap { it }
        .filterNotNull()
        .mapNotNull { it as? com.google.api.services.drive.model.Drive }
        .map { SharedDrive(it.id, it.name) }
        .toList()

fun getFolderId(
    folderName: String,
    driveId: String,
): String? {
    val list = driveService.files().list()
        .setQ("name = '$folderName' and mimeType = '$FOLDER_MIME_TYPE' and parents in '$driveId' and trashed = false")
        .setSupportsAllDrives(true)
        .setIncludeItemsFromAllDrives(true)
        .setFields("files(id)")
        .execute()
        .files

    if (list.size > 1) {
        error("Folder $folderName was found multiple times: ${list.map { it.id }}")
    }

    return list.firstOrNull()?.id
}

fun listFiles(
    driveId: String,
    parentName: String? = null,
    fileNamePattern: String? = null,
    mimeType: String? = null,
    sort: SortFiles? = null,
): List<SharedDriveFile> =
    listFilesRaw(driveId, parentName, fileNamePattern, mimeType, sort)
        .map { SharedDriveFile(it.id, it.name) }

fun listFilesRaw(
    driveId: String,
    parentName: String? = null,
    fileNamePattern: String? = null,
    mimeType: String? = null,
    sort: SortFiles? = null,
): MutableList<File> {
    val parentId = parentName?.let { getFolderId(it, driveId) ?: error("Folder $it was not found.") }
    val parentQuery = parentId?.let { "'$parentId' in parents" }
    val fileNameQuery = fileNamePattern?.let { "name contains '$it'" }
    val mimeTypeQuery = mimeType?.let { "mimeType = '$it'" }
    val trashedQuery = "trashed = false"
    val q = listOfNotNull(parentQuery, fileNameQuery, mimeTypeQuery, trashedQuery)
        .joinToString(separator = " and ")
        .takeIf { it.isNotEmpty() }

    return driveService.files().list()
        .setQ(q)
        .setDriveId(driveId)
        .setCorpora("drive")
        .setSupportsAllDrives(true)
        .setIncludeItemsFromAllDrives(true)
        .setFields("files(id, name, mimeType)")
        .setOrderBy(sort?.toQuery())
        .execute()
        .files
}

fun getFileContents(fileId: String): String =
    getFileContentAsBytes(fileId)
        .toString(Charsets.UTF_8)

fun getFileContentAsBytes(fileId: String): ByteArray =
    ByteArrayOutputStream()
        .apply {
            driveService.files()
                .get(fileId)
                .executeMediaAndDownloadTo(this)
        }
        .toByteArray()

data class SharedDrive(val id: String, val name: String)

data class SharedDriveFile(val id: String, val name: String)

data class SortFiles(val by: SortFileFields, val direction: SortFileDirection)

private fun SortFiles.toQuery() = "${by.key} ${direction.name.lowercase()}"

enum class SortFileFields(val key: String) {
    CREATED_AT("createdTime"),
    MODIFIED_AT("modifiedTime"),
}

enum class SortFileDirection {
    DESC,
    ASC,
}
