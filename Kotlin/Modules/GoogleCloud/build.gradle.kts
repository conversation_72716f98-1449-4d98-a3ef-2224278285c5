plugins {
    id("hero.kotlin-conventions")
}

val projectModule: (String) -> String by extra

dependencies {
    api(enforcedPlatform("com.google.cloud:libraries-bom:_"))
    api("com.google.cloud:google-cloud-pubsub:_")
    api("com.google.firebase:firebase-admin:_")
    api("com.google.cloud:google-cloud-firestore:_")
    api("com.google.cloud:google-cloud-bigquery:_")

    api("com.google.auth:google-auth-library-credentials:_")
    api("com.google.auth:google-auth-library-oauth2-http:_")

    implementation("com.google.api-client:google-api-client:_")
    implementation("com.google.apis:google-api-services-sheets:_")
    implementation("com.google.apis:google-api-services-drive:_")

    implementation(projectModule(":Modules:BaseUtils"))
    implementation(projectModule(":Modules:Exceptions"))
    testImplementation(projectModule(":Modules:Testing"))
    testImplementation("org.testcontainers:gcloud:_")
}

tasks.withType<GenerateModuleMetadata> {
    // allows to publish a module with google libraries-bom above
    suppressedValidationErrors.add("enforced-platform")
}
