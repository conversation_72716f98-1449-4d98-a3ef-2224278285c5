plugins {
    id("hero.kotlin-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    implementation(projectModule(":Modules:BaseUtils"))
    implementation(projectModule(":Modules:Exceptions"))
    implementation(projectModule(":Modules:Model"))
    implementation("org.jooq:jooq-postgres-extensions:_")

    api(projectModule(":Modules:SQL"))

    testImplementation(projectModule(":Modules:Testing"))
    testImplementation("org.testcontainers:gcloud:_")
    testImplementation("org.testcontainers:postgresql:_")
    testImplementation("org.postgresql:postgresql:_")
}
