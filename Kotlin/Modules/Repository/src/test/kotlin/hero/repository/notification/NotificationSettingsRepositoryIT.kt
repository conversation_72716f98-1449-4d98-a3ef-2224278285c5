package hero.repository.notification

import hero.model.NotificationsEnabled
import hero.repository.RepositoryTest
import hero.repository.user
import hero.sql.jooq.Tables.NOTIFICATION_SETTINGS
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class NotificationSettingsRepositoryIT : RepositoryTest() {
    @Nested
    inner class Save {
        @Test
        fun `should save notification settings for given user`() {
            val underTest = NotificationSettingsRepository(testContext)

            createUser(
                user(
                    "cestmir",
                    notificationsEnabled = NotificationsEnabled(
                        emailNewPost = false,
                        emailNewDm = false,
                        pushNewComment = false,
                        pushNewPost = false,
                        newsletter = false,
                        termsChanged = false,
                    ),
                ),
            )

            underTest.save(
                "cestmir",
                NotificationsEnabled(
                    emailNewPost = true,
                    emailNewDm = true,
                    pushNewComment = true,
                    pushNewPost = true,
                    newsletter = true,
                    termsChanged = true,
                ),
            )

            val exportedNotificationSettings = testContext
                .selectFrom(NOTIFICATION_SETTINGS)
                .fetchSingle()

            assertThat(exportedNotificationSettings[NOTIFICATION_SETTINGS.USER_ID]).isEqualTo("cestmir")

            assertThat(exportedNotificationSettings[NOTIFICATION_SETTINGS.EMAIL_NEW_POST]).isTrue()
            assertThat(exportedNotificationSettings[NOTIFICATION_SETTINGS.EMAIL_NEW_DM]).isTrue()
            assertThat(exportedNotificationSettings[NOTIFICATION_SETTINGS.PUSH_NEW_POST]).isTrue()
            assertThat(exportedNotificationSettings[NOTIFICATION_SETTINGS.PUSH_NEW_COMMENT]).isTrue()
            assertThat(exportedNotificationSettings[NOTIFICATION_SETTINGS.NEWSLETTER]).isTrue()
            assertThat(exportedNotificationSettings[NOTIFICATION_SETTINGS.TERMS_CHANGED]).isTrue()
        }
    }

    @Nested
    inner class GetByUserId {
        @Test
        fun `should get notification settings for given user`() {
            val underTest = NotificationSettingsRepository(testContext)

            val expectedNotificationSettings = NotificationsEnabled(
                emailNewPost = false,
                emailNewDm = false,
                pushNewComment = false,
                pushNewPost = false,
                newsletter = false,
                termsChanged = false,
            )
            createUser(user("cestmir", notificationsEnabled = expectedNotificationSettings))

            val result = underTest.getByUserId("cestmir")

            assertThat(result).isEqualTo(expectedNotificationSettings)
        }
    }
}
