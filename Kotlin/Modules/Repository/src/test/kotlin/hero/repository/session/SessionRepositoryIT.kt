package hero.repository.session

import hero.model.SignInProvider
import hero.repository.RepositoryTest
import hero.repository.session
import hero.repository.user
import hero.sql.jooq.Tables.SESSION
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID

class SessionRepositoryIT : RepositoryTest() {
    @Nested
    inner class Save {
        @Test
        fun `should save a session to database`() {
            val underTest = SessionRepository(testContext)
            val user = user(id = "user-id")
            val session = session(
                userId = user.id,
                id = UUID.fromString("bd028dab-451e-48ed-9dde-111d2e40e183"),
                userAgent = "curl 1.0",
                ipAddress = "127.0.0.1",
                signInLocation = "Prague, Czech Republic",
                createdAt = Instant.ofEpochSecond(**********),
                refreshedAt = Instant.ofEpochSecond(**********),
                revoked = false,
                deviceId = "device-id",
                signInProvider = SignInProvider.PASSWORD,
            )

            underTest.save(session)

            val sessionResult = testContext.selectFrom(SESSION).fetchSingle()

            assertThat(sessionResult[SESSION.ID]).isEqualTo(UUID.fromString(session.id))
            assertThat(sessionResult[SESSION.USER_ID]).isEqualTo(session.userId)
            assertThat(sessionResult[SESSION.USER_AGENT]).isEqualTo("curl 1.0")
            assertThat(sessionResult[SESSION.IP_ADDRESS].toString()).isEqualTo("127.0.0.1")
            assertThat(sessionResult[SESSION.SIGN_IN_LOCATION]).isEqualTo("Prague, Czech Republic")
            assertThat(sessionResult[SESSION.CREATED_AT]).isEqualTo(session.createdAt)
            assertThat(sessionResult[SESSION.REFRESHED_AT]).isEqualTo(session.refreshedAt)
            assertThat(sessionResult[SESSION.REVOKED]).isFalse()
            assertThat(sessionResult[SESSION.DEVICE_ID]).isEqualTo("device-id")
            assertThat(sessionResult[SESSION.SIGN_IN_PROVIDER]).isEqualTo(SignInProvider.PASSWORD.name)
        }
    }

    @Nested
    inner class GetById {
        @Test
        fun `should get session by id`() {
            val underTest = SessionRepository(testContext)
            val user = user(id = "user-id")
            val session = session(user.id)

            underTest.save(session)

            val result = underTest.getById(UUID.fromString(session.id))

            assertThat(result).isEqualTo(session)
        }
    }

    @Nested
    inner class FindById {
        @Test
        fun `should get session by id`() {
            val underTest = SessionRepository(testContext)
            val user = user(id = "user-id")
            val session = session(user.id)

            underTest.save(session)

            val result = underTest.findById(UUID.fromString(session.id))

            assertThat(result).isEqualTo(session)
        }

        @Test
        fun `should return null if session does not exist`() {
            val underTest = SessionRepository(testContext)
            val user = user(id = "user-id")
            val session = session(user.id)

            underTest.save(session)

            val result = underTest.findById(UUID.randomUUID())

            assertThat(result).isNull()
        }
    }
}
