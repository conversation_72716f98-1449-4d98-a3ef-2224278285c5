package hero.repository.post

import hero.model.Poll
import hero.model.PollOption
import hero.repository.RepositoryTest
import hero.repository.user
import hero.sql.jooq.Tables.POLL
import hero.sql.jooq.Tables.POLL_OPTION
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class PollRepositoryIT : RepositoryTest() {
    @Nested
    inner class FindById {
        @Test
        fun `should find poll by id`() {
            val underTest = PollRepository(testContext)

            createUser(user(id = "cestmir"))

            testContext
                .insertInto(POLL)
                .set(POLL.ID, "poll-id")
                .set(POLL.USER_ID, "cestmir")
                .set(POLL.DEADLINE, Instant.ofEpochSecond(1752820340))
                .execute()

            testContext
                .insertInto(POLL_OPTION)
                .set(POLL_OPTION.ID, "poll-option-id")
                .set(POLL_OPTION.POLL_ID, "poll-id")
                .set(POLL_OPTION.VOTE_COUNT, 49)
                .set(POLL_OPTION.TITLE, "best option")
                .execute()

            val fetchedPoll = underTest.findById("poll-id")

            assertThat(fetchedPoll).isEqualTo(
                Poll(
                    "poll-id",
                    Instant.ofEpochSecond(1752820340),
                    listOf(PollOption("poll-option-id", "best option", 49)),
                ),
            )
        }
    }
}
