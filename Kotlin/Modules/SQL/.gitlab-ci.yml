Kotlin/Modules/SQL/build:
  stage: build-modules
  needs:
    - Kotlin/Modules/BaseUtils/build
  extends:
    - .Kotlin/job-build-gradle-module

.migrate-job-template:
  needs:
    - Kotlin/Modules/SQL/build
  script:
    - set +e
    - curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.8.1/cloud-sql-proxy.linux.amd64
    - chmod +x cloud-sql-proxy
    - ./cloud-sql-proxy heroheroco:europe-west1:postgresql-instance --auto-iam-authn &
    - gradle $FLYWAY_MIGRATE_TASK; export MIGRATION_STATUS=${PIPESTATUS[0]}
    - kill $!
    - if [ $MIGRATION_STATUS -eq 0 ]; then exit 0; else exit 1; fi

Kotlin/Modules/SQL/migrate-devel:
  stage: deploy-devel
  extends:
    - .run-always-on-main-or-manually
    - .migrate-job-template
  variables:
    FLYWAY_MIGRATE_TASK: flywayMigrateDevel

Kotlin/Modules/SQL/migrate-staging:
  stage: deploy-staging
  extends:
    - .run-always-on-main-or-manually
    - .migrate-job-template
  variables:
    FLYWAY_MIGRATE_TASK: flywayMigrateStaging

Kotlin/Modules/SQL/migrate-prod:
  stage: deploy-prod
  extends:
    - .run-only-manually
    - .migrate-job-template
  variables:
    FLYWAY_MIGRATE_TASK: flywayMigrateProd
