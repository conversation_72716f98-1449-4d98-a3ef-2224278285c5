package hero.sql.jooq.queries

import hero.sql.jooq.queries.FetchDailySubscriberStats.subscriptionDate
import hero.sql.jooq.tables.Charge.CHARGE
import hero.sql.jooq.tables.Subscription.SUBSCRIPTION
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.Record7
import org.jooq.SelectHavingStep
import org.jooq.TableField
import org.jooq.impl.DSL
import org.jooq.impl.SQLDataType
import java.sql.Date
import java.time.Instant
import java.time.LocalDate

object FetchDailySubscriberStats {
    val subscriptionDate = DSL.field(DSL.name("date_series", "date"), SQLDataType.TIMESTAMPWITHTIMEZONE)
    val totalIncomeCents = DSL.field(DSL.name("total_income"), SQLDataType.INTEGER)
    val totalSubscriptions = DSL.count(
        DSL
            // if we are processing current day, or in the future, we count only subscriptions that have active status
            .`when`(date.ge(DSL.currentDate()).and(activeStatus), SUBSCRIPTION.STRIPE_ID)
            // if we are processing day in the past, we count every ended subscription and every active subscription
            // I am not sure if these conditions are actually needed, maybe only the date check is necessary
            .`when`(
                date.lt(DSL.currentDate())
                    .and(SUBSCRIPTION.ENDED_AT.le(DSL.currentInstant()).or(activeStatus)),
                SUBSCRIPTION.STRIPE_ID,
            ),
    ).`as`("total_subs")

    /**
     * Represents number of active subscribers on given day. Active subscriber is a subscriber that had active subscription
     * and hadn't canceled on that day yet.
     * ```
     * select count(case
     *                  when status in ('active', 'past_due') and (cancelled_at is null or cancelled_at::date > '2024-04-01')
     *                      then stripe_id
     *                  when ended_at < current_timestamp and cancelled_at::date > '2024-04-01'
     *                      then stripe_id
     *     end)
     * from subscription
     * where creator_id = 'cestmirstrakatyheroherorpkjaolu'
     *   and '2024-04-01' between started_at::date and coalesce(ended_at::date, ends_at::date)
     *   and status not in ('incomplete', 'incomplete_expired');
     * ```
     *
     */
    val totalActiveSubscriptions = DSL.count(
        DSL
            .`when`(
                activeStatus.and(SUBSCRIPTION.CANCELLED_AT.isNull.or(SUBSCRIPTION.CANCELLED_AT.asDate().gt(date))),
                SUBSCRIPTION.STRIPE_ID,
            )
            .`when`(
                SUBSCRIPTION.ENDED_AT.lt(DSL.currentInstant()).and(SUBSCRIPTION.CANCELLED_AT.asDate().gt(date)),
                SUBSCRIPTION.STRIPE_ID,
            ),
    ).`as`("total_active_subs")
    val createdOnDay = DSL.count(
        DSL.`when`(
            date.eq(SUBSCRIPTION.STARTED_AT.asDate()),
            SUBSCRIPTION.STRIPE_ID,
        ),
    ).`as`("created_on_day")

    val cancelledOnDay = DSL.count(
        DSL.`when`(
            date.eq(SUBSCRIPTION.CANCELLED_AT.asDate()),
            SUBSCRIPTION.STRIPE_ID,
        ),
    ).`as`("cancelled_on_day")

    private val percent = DSL.value(1).minus(DSL.coalesce(SUBSCRIPTION.COUPON_PERCENT_OFF, 0).div(100.0))

    // price_cents *
    // (
    //   case
    //     when coupon_expires_at is null then 1
    //     when date > coupon_expires_at then 1
    //     else 1 - (coalesce(coupon_percent_off, 0) / 100.0)
    // )
    val priceCents = SUBSCRIPTION.PRICE_CENTS.times(
        DSL
            .`when`(SUBSCRIPTION.COUPON_EXPIRES_AT.isNull, percent)
            .`when`(date.gt(SUBSCRIPTION.COUPON_EXPIRES_AT.asDate()), 1)
            .otherwise(percent),
    )

    fun query(
        context: DSLContext,
        creatorId: String,
        from: Instant,
        to: Instant,
        includeCancelled: Boolean = true,
    ): SelectHavingStep<Record7<LocalDate, String, Int, Int, Int, Int, Int>> {
        val fromDate = DSL.timestamp(Date.from(from))
        val toDate = DSL.timestamp(Date.from(to))
        val dateSeries = DSL.table(
            "generate_series({0}, {1}, '1 day')",
            fromDate,
            toDate,
        ).`as`("date_series", "date")

        val creatorIdCondition = SUBSCRIPTION.CREATOR_ID.eq(creatorId)
        val joinSubsCondition = creatorIdCondition
            .and(
                date.between(
                    SUBSCRIPTION.STARTED_AT.asDate(),
                    DSL.coalesce(SUBSCRIPTION.ENDED_AT.asDate(), SUBSCRIPTION.ENDS_AT.asDate()),
                ),
            )
            .and(ignoreStatus)
            .let {
                if (includeCancelled) {
                    it
                } else {
                    it.and(SUBSCRIPTION.CANCELLED_AT.isNull)
                }
            }

        return context
            .select(
                subscriptionDate.cast(SQLDataType.LOCALDATE),
                DSL.value(creatorId),
                totalSubscriptions,
                totalActiveSubscriptions,
                createdOnDay,
                cancelledOnDay,
                getTotalIncomeCentsField(creatorId),
            )
            .from(
                dateSeries
                    .leftJoin(SUBSCRIPTION)
                    .on(joinSubsCondition),
            )
            .groupBy(subscriptionDate)
    }

    private fun getTotalIncomeCentsField(creatorId: String) =
        DSL.coalesce(
            DSL.select(DSL.sum(DSL.coalesce(CHARGE.TRANSFER_AMOUNT, CHARGE.AMOUNT)))
                .from(CHARGE)
                .where(CHARGE.CREATOR_ID.eq(creatorId))
                .and(date.eq(CHARGE.STRIPE_CREATED_AT.asDate()))
                .and(CHARGE.STATUS.eq("succeeded")),
            0,
        ).cast(SQLDataType.INTEGER).`as`("total_income")
}

private fun <R : Record, T> TableField<R, T>.asDate() = this.cast(SQLDataType.DATE)

private val date = subscriptionDate.cast(SQLDataType.DATE)
private val activeStatus = SUBSCRIPTION.STATUS.`in`("active", "past_due", "trialing")
private val ignoreStatus = SUBSCRIPTION.STATUS.notIn("incomplete", "incomplete_expired")
