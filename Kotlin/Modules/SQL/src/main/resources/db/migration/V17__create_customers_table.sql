CREATE TABLE customer
(
    stripe_id              TEXT PRIMARY KEY,
    user_id                TEXT        NOT NULL,
    created_at             TIMESTAMPTZ NOT NULL,
    currency               TEXT        NOT NULL,
    name                   TEXT        NULL,
    email                  TEXT        NULL,
    default_payment_method TEXT        NULL
);

COMMENT ON COLUMN customer.name is 'This column is replicates name field from user firestore collection';
COMMENT ON COLUMN customer.email is 'This column is replicates email field from users firestore collection';

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE customer TO "<EMAIL>";
        END IF;
    END
$$;
