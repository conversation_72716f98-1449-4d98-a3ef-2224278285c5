CREATE TABLE subscribe_request
(
    id          BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id     TEXT        NOT NULL,
    creator_id  TEXT        NOT NULL,
    created_at  TIMESTAMPTZ NOT NULL,
    updated_at  TIMESTAMPTZ NOT NULL,
    accepted_at TIMESTAMPTZ NULL,
    declined_at TIMESTAMPTZ NULL,
    deleted_at  TIMESTAMPTZ NULL,

    CONSTRAINT "fe06481b08e545d681afbeaf11f60541_fk" FOREIGN KEY (user_id) REFERENCES "user" (id),
    CONSTRAINT "661ba49a0e734abd91d0d24175eaab35_fk" FOREIGN KEY (creator_id) REFERENCES "user" (id)
);

CREATE INDEX "6ed0759ca27b4099bbc61882fe3b3ce8_ix" ON subscribe_request (user_id);
CREATE INDEX "acde9e61c2f945858056927010fb54f0_ix" ON subscribe_request (user_id) WHERE accepted_at IS NULL AND declined_at IS NULL AND deleted_at IS NULL;
CREATE UNIQUE INDEX "352951ae9db44ebdb3663a5e886baf3a_ux" ON subscribe_request (user_id, creator_id) WHERE accepted_at IS NULL AND declined_at IS NULL AND deleted_at IS NULL;
