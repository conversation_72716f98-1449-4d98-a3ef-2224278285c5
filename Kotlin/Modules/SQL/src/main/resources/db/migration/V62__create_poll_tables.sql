CREATE TABLE poll
(
    id       TEXT PRIMARY KEY,
    deadline TIMESTAMPTZ NULL
);

CREATE TABLE poll_option
(
    id         TEXT PRIMARY KEY,
    poll_id    TEXT NOT NULL,
    title      TEXT NOT NULL,
    vote_count INT  NOT NULL,

    CONSTRAINT "823e857256e34b11bca812679a52777c_fk" FOREIGN KEY (poll_id) REFERENCES poll (id)
);

CREATE INDEX "048164e8435545aba6af0621476ce0b9_ix" ON poll_option (poll_id);

CREATE TABLE poll_option_vote
(
    poll_option_id TEXT NOT NULL,
    user_id        TEXT NOT NULL,

    CONSTRAINT "33e0fe3583b84688901ffb0fe84b3e06_fk" FOREIGN KEY (poll_option_id) REFERENCES poll_option (id) ON DELETE CASCADE,
    CONSTRAINT "7fc085fabe2e4bd6b4a72b86c306e401_fk" FOR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES "user" (id),
    PRIMARY KEY (poll_option_id, user_id)
);

CREATE INDEX "137bb70ff409494fa6847031f38ac287_ix" ON poll_option (poll_id);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE poll TO "<EMAIL>";
            GRANT ALL ON TABLE poll_option TO "<EMAIL>";
            GRANT ALL ON TABLE poll_option_vote TO "<EMAIL>";
        END IF;
    END
$$;
