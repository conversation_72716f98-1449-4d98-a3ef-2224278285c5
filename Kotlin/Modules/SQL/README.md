## Generate JooqSQL classes

To run the project, you need to generate jooq classes in `generated-src`. This is done either by whole `gradle build` or
faster with `gradle :Modules:SQL:generateJooq`. Docker environment is required.

In case you get:

> Previous attempts to find a Docker environment failed. Will not retry. Please see logs and check configuration

You will need to kill all available Gradle daemons.:

```shell
gradle --stop
```

If you are using IntellIJ gradle, you can search for Action `Show Gradle Daemons` and kill them for here.

## Connect via Cloud SQL Proxy
### 1. Install Cloud SQL Proxy

``` shell
curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.10.1/cloud-sql-proxy.linux.amd64
chmod +x cloud-sql-proxy
```
see https://cloud.google.com/sql/docs/mysql/connect-auth-proxy

or via brew
```shell
brew install cloud-sql-proxy
```

### 2. Run the proxy
To start, you need to bind Google credentials to env var:

```shell
export GOOGLE_APPLICATION_CREDENTIALS=/Users/<USER>/.config/gcloud/cloud-run-herohero.json
```

Then, you can either bind the remote instance to a unix socket:
``` shell
cloud-sql-proxy --unix-socket ~/.cloudsql heroheroco:europe-west1:postgresql-instance --auto-iam-authn
```

Or to a TCP port:

```shell
cloud-sql-proxy heroheroco:europe-west1:postgresql-instance --auto-iam-authn
```

To test your connection, you can connect with `psql` (via TCP):

```shell
psql --host=127.0.0.1 --username=<EMAIL> devel-herohero-db
```

### 3. Set environment variables
To be able to connect with Herohero application, add these variables into your .env or export them into your shell:
```
SQL_PROXY_UNIX_SOCKET=/path/to/unix-socket/.cloudsql/heroheroco:europe-west1:postgresql-instance/.s.PGSQL.5432
SQL_INSTANCE_NAME=heroheroco:europe-west1:postgresql-instance
SQL_DATABASE_NAME=devel-herohero-db
```

## Connect to local database
Assuming you have local PostgreSQL instance running:
### 1. Create database
```sql
CREATE DATABASE local-herohero-db;
```

### 2. Migrate database
```shell
gradle flywayMigrateLocal
```

In case you set different password, you can export env variable `POSTGRES_USER_PWD`

### 3. Set environment variables
```
SQL_INSTANCE_NAME=sql-instance-name
SQL_DATABASE_NAME=local-herohero-db
```
