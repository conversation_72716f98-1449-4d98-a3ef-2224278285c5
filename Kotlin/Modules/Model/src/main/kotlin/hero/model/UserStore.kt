package hero.model

import hero.core.data.EntityCollection
import java.time.Instant

data class UserStore(
    val id: String? = null,
    val attributes: UserStoreAttributes = UserStoreAttributes(),
    val relationships: Map<String, Any?> = emptyMap(),
) {
    val type: String = "store"

    companion object : EntityCollection<UserStore> {
        override val collectionName: String = "store"
    }
}

data class UserStoreAttributes(
    val content: Map<String, Any?> = mapOf(),
    val updatedAt: Instant? = null,
    val version: String? = null,
)
