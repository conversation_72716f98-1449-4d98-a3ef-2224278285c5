package hero.model

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SlackAttachment(
    val title: String,
    @JsonProperty("image_url")
    val imageUrl: String,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SlackBlockText(
    val text: String,
    val type: String = "mrkdwn",
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SlackAccessory(
    @JsonProperty("image_url")
    val imageUrl: String,
    @JsonProperty("alt_text")
    val altText: String,
    val type: String = "image",
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SlackBlock(
    val text: SlackBlockText,
    val accessory: SlackAccessory? = null,
    val type: String = "section",
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SlackMessage(
    val channel: String,
    val text: String? = null,
    val attachments: List<SlackAttachment>? = null,
    val blocks: List<SlackBlock>,
)
