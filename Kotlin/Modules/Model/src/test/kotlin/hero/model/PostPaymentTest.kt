package hero.model

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant

class PostPaymentTest {
    @Test
    fun `post payment should have an id in correct format`() {
        val underTest = PostPayment(userId = "cestmir", postId = "paidMessage", timestamp = Instant.now())

        assertThat(underTest.id).isEqualTo("pp-paidMessage-cestmir")
        assertThat(PostPayment.id(userId = "cestmir", postId = "paidMessage")).isEqualTo(underTest.id)
    }
}
