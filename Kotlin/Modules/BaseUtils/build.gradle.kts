plugins {
    id("hero.kotlin-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    api(projectModule(":<PERSON><PERSON><PERSON>:<PERSON>"))
    api("com.github.kittinunf.fuel:fuel:_")
    api("com.github.kittinunf.fuel:fuel-jackson:_")

    api("commons-codec:commons-codec:_")
    // needed for RandomStringUtils
    api("org.apache.commons:commons-text:_")
    api("javax.validation:validation-api:_")

    implementation("com.vdurmont:emoji-java:_")

    // doesn't belong here
    api(enforcedPlatform("com.google.cloud:libraries-bom:_"))
    api("com.google.cloud:google-cloud-logging:_")

    testImplementation(projectModule(":Modules:Testing"))
}

tasks.withType<GenerateModuleMetadata> {
    // allows to publish a module with google libraries-bom above
    suppressedValidationErrors.add("enforced-platform")
}
