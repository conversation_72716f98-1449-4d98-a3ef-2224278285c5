plugins {
    id("hero.kotlin-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    api(projectModule(":Modules:Testing"))
    api(projectModule(":Modules:Repository"))
    implementation(projectModule(":Modules:Model"))
    implementation(projectModule(":Modules:BaseUtils"))
    implementation(projectModule(":Modules:SQL"))
    implementation(projectModule(":Modules:GoogleCloud"))

    implementation("org.testcontainers:gcloud:_")
    implementation("com.stripe:stripe-java:_")
    implementation("org.testcontainers:postgresql:_")
    implementation("org.postgresql:postgresql:_")
}
