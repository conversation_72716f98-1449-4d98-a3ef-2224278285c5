package hero.common

import com.google.auth.oauth2.GoogleCredentials
import com.google.firebase.FirebaseApp
import com.google.firebase.FirebaseOptions

fun loadFirebase(
    projectId: String,
    envPrefix: String,
    appName: String,
): FirebaseApp {
    val firebaseAuthProjectId = "$envPrefix-$projectId"
    val credentialsFileName = "$firebaseAuthProjectId.json"
    val credentials = object {}::class.java.classLoader.getResourceAsStream(credentialsFileName)?.buffered()
        ?: error("Failed to load credentials for $firebaseAuthProjectId")
    val options = FirebaseOptions
        .builder()
        .setProjectId(firebaseAuthProjectId)
        .setCredentials(GoogleCredentials.fromStream(credentials))
        .build()

    return FirebaseApp.initializeApp(options, appName)
}
