package hero.messaging

import com.google.firebase.ErrorCode
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.MulticastMessage
import hero.common.loadFirebase
import hero.core.logging.Logger
import io.sentry.Sentry

fun initFirebaseMessaging(
    projectId: String,
    envPrefix: String,
): FirebaseMessaging {
    val firebase = loadFirebase(projectId, envPrefix, "messaging")
    return FirebaseMessaging.getInstance(firebase)
}

fun sendMulticastMessage(
    firebaseMessaging: FirebaseMessaging,
    messageBuilder: MulticastMessage.Builder,
    tokens: List<String>,
    userId: String,
    log: Logger,
) {
    val message = messageBuilder.addAllTokens(tokens).build()
    val multicastResponse = firebaseMessaging.sendEachForMulticast(message)
    val retryTokens = mutableListOf<String>()
    multicastResponse.responses.forEachIndexed { index, response ->
        if (response.exception != null && response.exception.errorCode !in setOf(ErrorCode.NOT_FOUND)) {
            if (response.exception.errorCode in retryCodes) {
                retryTokens.add(tokens[index])
            }
        }
    }

    if (retryTokens.isNotEmpty()) {
        log.info("Retrying sending push notification to $retryTokens")
        val retryMulticastResponse = firebaseMessaging.sendEachForMulticast(message)
        retryMulticastResponse.responses
            .mapNotNull { it.exception }
            .forEach {
                if (it.errorCode !in setOf(ErrorCode.NOT_FOUND)) {
                    log.fatal(
                        "Failed to send push notification to $userId even after retrying",
                        cause = it,
                    )
                    // we capture the exception for now, so we can better debug which exceptions are retryable
                    Sentry.captureException(it)
                }
            }
    }
}

private val retryCodes = setOf(ErrorCode.UNKNOWN, ErrorCode.INTERNAL, ErrorCode.UNAVAILABLE)
